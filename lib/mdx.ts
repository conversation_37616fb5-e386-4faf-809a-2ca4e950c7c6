/// <reference types="node" />

import fs from 'node:fs';
import path from 'node:path';
import matter from 'gray-matter';
import type { Post, PostFile } from '@/types/mdx';

export type { Post, PostFile } from '@/types/mdx';

const POSTS_DIRECTORY = path.join(process.cwd(), 'content/blog');

const readMDXFile = (filePath: string): PostFile => {
  if (!fs.existsSync(filePath)) {
    throw new Error(`File does not exist: ${filePath}`);
  }

  const content = fs.readFileSync(filePath, 'utf8');
  if (!content || content.trim() === '') {
    throw new Error(`File is empty: ${filePath}`);
  }

  const name = path.basename(filePath, '.mdx');
  return { name, content };
};

const getMDXFiles = (dir: string): PostFile[] => {
  try {
    if (!fs.existsSync(dir)) {
      return [];
    }

    const files = fs.readdirSync(dir);

    const mdxFiles = files.filter((file: string) => file.endsWith('.mdx'));

    return mdxFiles.map((file: string) => {
      return readMDXFile(path.join(dir, file));
    });
  } catch (_error) {
    return [];
  }
};

export function getPost(slug: string): Post | null {
  try {
    const fullPath = path.join(POSTS_DIRECTORY, `${slug}.mdx`);

    // Check if file exists first
    if (!fs.existsSync(fullPath)) {
      return null;
    }

    const { content, name } = readMDXFile(fullPath);

    try {
      // Use a try-catch specifically for frontmatter parsing
      const { data, content: mdxContent } = matter(content);

      const { title, publishedAt, location, image, summary } = data;

      if (!title) {
        return null;
      }

      if (!publishedAt) {
        return null;
      }

      const pubDate = new Date(publishedAt);
      if (Number.isNaN(pubDate.getTime())) {
        return null;
      }

      const stats = fs.statSync(fullPath);

      // Check that MDX content is valid
      if (!mdxContent || mdxContent.trim() === '') {
        return null;
      }
      return {
        slug: name,
        title,
        publishedAt: pubDate,
        location: location || '',
        image: image || '',
        summary: summary || '',
        content: mdxContent.trim(),
        updatedAt: stats.mtime,
      };
    } catch (_matterError) {
      return null;
    }
  } catch (_error) {
    return null;
  }
}

export function getAllPosts(): Post[] {
  const files = getMDXFiles(POSTS_DIRECTORY);
  const posts = files.map((file) => {
    const post = getPost(file.name);
    return post;
  });

  return posts
    .filter((post): post is Post => post !== null)
    .sort((a, b) => {
      const dateA = new Date(a.publishedAt).getTime();
      const dateB = new Date(b.publishedAt).getTime();
      return dateB - dateA;
    });
}
