import Link from 'next/link';

export const metadata = {
  title: '<PERSON>ls <PERSON> <PERSON>',
  description: 'A collection of useful tools for founders and makers.',
};

export default function ToolsPage() {
  return (
    <div className="grow pt-12 pb-16 md:pt-16 md:pb-20">
      <div className="max-w-[700px]">
        <div className="space-y-10">
          <section>
            <h1 className="mb-4 font-bold font-geist text-slate-800 text-xl leading-tight md:text-2xl dark:text-white">
              Founder Tools
            </h1>
            <p className="mb-8 text-slate-500 text-sm dark:text-slate-400">
              A collection of useful tools to help you make better decisions in
              your founder journey.
            </p>

            <div className="space-y-4">
              <Link
                className="block rounded-[4px] border border-slate-200 bg-white p-4 transition-colors hover:border-slate-300 dark:border-slate-800 dark:bg-gray-900 dark:hover:border-slate-700"
                href="/tools/build-vs-buy"
              >
                <div className="space-y-2">
                  <h2 className="font-semibold text-lg text-slate-800 dark:text-white">
                    Build vs Buy Calculator
                  </h2>
                  <p className="text-slate-500 text-sm dark:text-slate-400">
                    Compare the financial trade-offs between building an
                    in-house solution and purchasing a SaaS product.
                  </p>
                </div>
              </Link>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
}
