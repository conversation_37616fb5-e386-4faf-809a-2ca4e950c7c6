'use client';

import { TrendingUp } from 'lucide-react';
import { type ChangeEvent, useState } from 'react';
import {
  Area,
  AreaChart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface FormData {
  teamSize: string;
  hourlyRate: string;
  buildTime: string;
  maintenanceHours: string;
  saasPrice: string;
  expectedUsers: string;
}

interface ChartDataPoint {
  month: number;
  buildCost: number;
  saasCost: number;
}

interface Results {
  initialBuildCost: number;
  yearlyMaintenanceCost: number;
  yearlyInfrastructureCost: number;
  yearOneBuildCost: number;
  yearlyOngoingBuildCost: number;
  yearlySubscriptionCost: number;
  threeYearBuildCost: number;
  threeYearSaasCost: number;
  monthlyPerUserBuildCost: number;
  monthlyPerUserSaasCost: number;
  breakEvenMonths: number;
  recommendation: string;
  recommendationConfidence: number;
  chartData: ChartDataPoint[];
  costDifference: number;
  isBuildCheaper: boolean;
}

export default function BuildVsBuyCalculator() {
  const [formData, setFormData] = useState<FormData>({
    teamSize: '',
    hourlyRate: '',
    buildTime: '',
    maintenanceHours: '',
    saasPrice: '',
    expectedUsers: '',
  });

  const [results, setResults] = useState<Results | null>(null);

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const calculateCosts = () => {
    // Initial build costs
    const initialBuildCost =
      Number(formData.buildTime) *
      Number(formData.hourlyRate) *
      Number(formData.teamSize);

    // Yearly maintenance costs (including team size)
    const yearlyMaintenanceCost =
      Number(formData.maintenanceHours) *
      12 *
      Number(formData.hourlyRate) *
      Number(formData.teamSize);

    // Infrastructure and tooling costs (estimated at 20% of development costs)
    const yearlyInfrastructureCost = initialBuildCost * 0.2;

    // First year total cost for building
    const yearOneBuildCost =
      initialBuildCost + yearlyMaintenanceCost + yearlyInfrastructureCost;

    // Subsequent years cost for building (maintenance + infrastructure)
    const yearlyOngoingBuildCost =
      yearlyMaintenanceCost + yearlyInfrastructureCost;

    // SaaS costs
    const yearlySubscriptionCost =
      Number(formData.saasPrice) * 12 * Number(formData.expectedUsers);

    // 3-year total costs
    const threeYearBuildCost = yearOneBuildCost + yearlyOngoingBuildCost * 2;
    const threeYearSaasCost = yearlySubscriptionCost * 3;

    // Calculate monthly per-user costs for comparison
    const monthlyPerUserBuildCost =
      threeYearBuildCost / (36 * Number(formData.expectedUsers));
    const monthlyPerUserSaasCost = Number(formData.saasPrice);

    // Break-even analysis (in months)
    const breakEvenMonths = Math.ceil(
      initialBuildCost / (yearlySubscriptionCost / 12)
    );

    // Generate monthly data points for the chart (36 months)
    let cumulativeBuildCost = 0;
    let cumulativeSaasCost = 0;

    const chartData = Array.from({ length: 36 }, (_, month) => {
      // Calculate build costs
      const buildCostForMonth = month === 0 ? initialBuildCost : 0;
      const maintenanceCostForMonth = yearlyMaintenanceCost / 12;
      const infrastructureCostForMonth = yearlyInfrastructureCost / 12;
      const monthlyBuildCost =
        buildCostForMonth +
        maintenanceCostForMonth +
        infrastructureCostForMonth;

      // Calculate SaaS costs
      const monthlySaasCost = yearlySubscriptionCost / 12;

      // Update cumulative costs
      cumulativeBuildCost += monthlyBuildCost;
      cumulativeSaasCost += monthlySaasCost;

      return {
        month: month + 1,
        buildCost: Math.round(cumulativeBuildCost),
        saasCost: Math.round(cumulativeSaasCost),
      };
    });

    setResults({
      initialBuildCost,
      yearlyMaintenanceCost,
      yearlyInfrastructureCost,
      yearOneBuildCost,
      yearlyOngoingBuildCost,
      yearlySubscriptionCost,
      threeYearBuildCost,
      threeYearSaasCost,
      monthlyPerUserBuildCost,
      monthlyPerUserSaasCost,
      breakEvenMonths,
      recommendation: threeYearBuildCost < threeYearSaasCost ? 'BUILD' : 'BUY',
      recommendationConfidence:
        (Math.abs(threeYearBuildCost - threeYearSaasCost) /
          Math.max(threeYearBuildCost, threeYearSaasCost)) *
        100,
      chartData,
      costDifference: Math.abs(threeYearBuildCost - threeYearSaasCost),
      isBuildCheaper: threeYearBuildCost < threeYearSaasCost,
    });
  };

  return (
    <section className="build-vs-buy-calculator mx-auto max-w-[700px]">
      <div className="pt-4 pb-6">
        <h1 className="mb-1.5 font-sans font-semibold text-2xl">
          Build vs Buy Calculator
        </h1>
        <p className="text-slate-500 text-sm dark:text-slate-400">
          Compare the financial trade-offs between building an in-house solution
          and purchasing a SaaS product. Make data-driven decisions for your
          software investments.
        </p>
      </div>

      <div className="mb-8 space-y-4">
        <div className="grid gap-4 md:grid-cols-2">
          <Card className="border border-slate-200 bg-white dark:border-slate-800 dark:bg-gray-900">
            <CardHeader>
              <CardTitle className="font-semibold text-lg">
                Build Cost
              </CardTitle>
              <CardDescription className="text-slate-500 dark:text-slate-400">
                Initial and ongoing costs for building in-house
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-3">
                <div className="space-y-1.5">
                  <Label
                    className="text-slate-500 dark:text-slate-400"
                    htmlFor="teamSize"
                  >
                    Team Size
                  </Label>
                  <Input
                    className="rounded-[4px] border-slate-200 bg-white text-slate-800 placeholder:text-slate-400 dark:border-slate-800 dark:bg-gray-900 dark:text-white dark:placeholder:text-slate-500"
                    id="teamSize"
                    name="teamSize"
                    onChange={handleInputChange}
                    placeholder="Number of developers"
                    type="number"
                    value={formData.teamSize}
                  />
                </div>
                <div className="space-y-1.5">
                  <Label
                    className="text-slate-500 dark:text-slate-400"
                    htmlFor="hourlyRate"
                  >
                    Hourly Rate
                  </Label>
                  <Input
                    className="rounded-[4px] border-slate-200 bg-white text-slate-800 placeholder:text-slate-400 dark:border-slate-800 dark:bg-gray-900 dark:text-white dark:placeholder:text-slate-500"
                    id="hourlyRate"
                    name="hourlyRate"
                    onChange={handleInputChange}
                    placeholder="Average hourly rate"
                    type="number"
                    value={formData.hourlyRate}
                  />
                </div>
                <div className="space-y-1.5">
                  <Label
                    className="text-slate-500 dark:text-slate-400"
                    htmlFor="buildTime"
                  >
                    Build Time (hours)
                  </Label>
                  <Input
                    className="rounded-[4px] border-slate-200 bg-white text-slate-800 placeholder:text-slate-400 dark:border-slate-800 dark:bg-gray-900 dark:text-white dark:placeholder:text-slate-500"
                    id="buildTime"
                    name="buildTime"
                    onChange={handleInputChange}
                    placeholder="Estimated development hours"
                    type="number"
                    value={formData.buildTime}
                  />
                </div>
                <div className="space-y-1.5">
                  <Label
                    className="text-slate-500 dark:text-slate-400"
                    htmlFor="maintenanceHours"
                  >
                    Monthly Maintenance (hours)
                  </Label>
                  <Input
                    className="rounded-[4px] border-slate-200 bg-white text-slate-800 placeholder:text-slate-400 dark:border-slate-800 dark:bg-gray-900 dark:text-white dark:placeholder:text-slate-500"
                    id="maintenanceHours"
                    name="maintenanceHours"
                    onChange={handleInputChange}
                    placeholder="Monthly maintenance hours"
                    type="number"
                    value={formData.maintenanceHours}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border border-slate-200 bg-white dark:border-slate-800 dark:bg-gray-900">
            <CardHeader>
              <CardTitle className="font-semibold text-lg">SaaS Cost</CardTitle>
              <CardDescription className="text-slate-500 dark:text-slate-400">
                Monthly subscription and user-based pricing
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-3">
                <div className="space-y-1.5">
                  <Label
                    className="text-slate-500 dark:text-slate-400"
                    htmlFor="saasPrice"
                  >
                    SaaS Monthly Price
                  </Label>
                  <Input
                    className="rounded-[4px] border-slate-200 bg-white text-slate-800 placeholder:text-slate-400 dark:border-slate-800 dark:bg-gray-900 dark:text-white dark:placeholder:text-slate-500"
                    id="saasPrice"
                    name="saasPrice"
                    onChange={handleInputChange}
                    placeholder="Per user monthly cost"
                    type="number"
                    value={formData.saasPrice}
                  />
                </div>
                <div className="space-y-1.5">
                  <Label
                    className="text-slate-500 dark:text-slate-400"
                    htmlFor="expectedUsers"
                  >
                    Expected Users
                  </Label>
                  <Input
                    className="rounded-[4px] border-slate-200 bg-white text-slate-800 placeholder:text-slate-400 dark:border-slate-800 dark:bg-gray-900 dark:text-white dark:placeholder:text-slate-500"
                    id="expectedUsers"
                    name="expectedUsers"
                    onChange={handleInputChange}
                    placeholder="Number of expected users"
                    type="number"
                    value={formData.expectedUsers}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <Button
          className="w-full rounded-[4px] bg-gray-900 text-white transition-colors hover:bg-gray-800 dark:bg-gray-800 dark:hover:bg-gray-700"
          onClick={calculateCosts}
          size="lg"
        >
          Calculate Costs
        </Button>

        {results && (
          <div className="space-y-4">
            <Card className="border border-slate-200 bg-white dark:border-slate-800 dark:bg-gray-900">
              <CardHeader>
                <CardTitle className="font-semibold text-lg">
                  Cost Comparison
                </CardTitle>
                <CardDescription className="text-slate-500 dark:text-slate-400">
                  3-year cost projection for build vs buy scenarios
                </CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <div className="h-[400px] w-full">
                  <ResponsiveContainer height="100%" width="100%">
                    <AreaChart
                      data={results.chartData}
                      margin={{ top: 16, right: 16, bottom: 0, left: 0 }}
                    >
                      <defs>
                        <linearGradient
                          id="buildCostGradient"
                          x1="0"
                          x2="0"
                          y1="0"
                          y2="1"
                        >
                          <stop
                            offset="0%"
                            stopColor="hsl(var(--chart-1))"
                            stopOpacity={0.5}
                          />
                          <stop
                            offset="100%"
                            stopColor="hsl(var(--chart-1))"
                            stopOpacity={0}
                          />
                        </linearGradient>
                        <linearGradient
                          id="saasCostGradient"
                          x1="0"
                          x2="0"
                          y1="0"
                          y2="1"
                        >
                          <stop
                            offset="0%"
                            stopColor="hsl(var(--chart-2))"
                            stopOpacity={0.5}
                          />
                          <stop
                            offset="100%"
                            stopColor="hsl(var(--chart-2))"
                            stopOpacity={0}
                          />
                        </linearGradient>
                      </defs>
                      <CartesianGrid
                        stroke="#e2e8f0"
                        strokeDasharray="3 3"
                        vertical={false}
                      />
                      <XAxis
                        axisLine={false}
                        dataKey="month"
                        fontSize={12}
                        stroke="#94a3b8"
                        tickLine={false}
                      />
                      <YAxis
                        axisLine={false}
                        fontSize={12}
                        stroke="#94a3b8"
                        tickFormatter={(value) => `$${value.toLocaleString()}`}
                        tickLine={false}
                      />
                      <Tooltip
                        content={({ active, payload }) => {
                          if (active && payload && payload.length > 0) {
                            const buildValue = payload[0]?.value;
                            const saasValue = payload[1]?.value;

                            return (
                              <div className="rounded-[4px] border border-slate-200 bg-white p-2 shadow-sm dark:border-slate-800 dark:bg-gray-900">
                                <div className="grid grid-cols-2 gap-2">
                                  <div className="flex items-center gap-1">
                                    <div className="h-2 w-2 rounded-full bg-[hsl(var(--chart-1))]" />
                                    <span className="font-medium text-slate-800 text-sm dark:text-white">
                                      Build:
                                    </span>
                                    <span className="text-slate-600 text-sm dark:text-slate-300">
                                      ${buildValue?.toLocaleString() ?? 0}
                                    </span>
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <div className="h-2 w-2 rounded-full bg-[hsl(var(--chart-2))]" />
                                    <span className="font-medium text-slate-800 text-sm dark:text-white">
                                      SaaS:
                                    </span>
                                    <span className="text-slate-600 text-sm dark:text-slate-300">
                                      ${saasValue?.toLocaleString() ?? 0}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            );
                          }
                          return null;
                        }}
                      />
                      <Area
                        dataKey="buildCost"
                        fill="url(#buildCostGradient)"
                        stroke="hsl(var(--chart-1))"
                        strokeWidth={2}
                        type="monotone"
                      />
                      <Area
                        dataKey="saasCost"
                        fill="url(#saasCostGradient)"
                        stroke="hsl(var(--chart-2))"
                        strokeWidth={2}
                        type="monotone"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
              <CardFooter className="border-slate-200 border-t bg-slate-50 dark:border-slate-800 dark:bg-gray-900/50">
                <div className="grid w-full gap-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <div className="font-semibold text-lg text-slate-800 dark:text-white">
                        {results.isBuildCheaper ? (
                          <>
                            Building in-house saves{' '}
                            <span className="text-green-600 dark:text-green-400">
                              ${results.costDifference.toLocaleString()}
                            </span>
                          </>
                        ) : (
                          <>
                            SaaS solution saves{' '}
                            <span className="text-green-600 dark:text-green-400">
                              ${results.costDifference.toLocaleString()}
                            </span>
                          </>
                        )}
                      </div>
                      <div className="text-slate-500 text-sm dark:text-slate-400">
                        over 3 years
                      </div>
                    </div>
                    <div className="flex items-center gap-1 text-green-600 dark:text-green-400">
                      <TrendingUp className="h-5 w-5" />
                      <span className="font-medium text-sm">
                        {Math.round(results.recommendationConfidence * 100)}%
                        confidence
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between border-slate-200 border-t pt-2 dark:border-slate-800">
                    <div className="space-y-1">
                      <div className="font-medium text-slate-800 text-sm dark:text-white">
                        Break-even point
                      </div>
                      <div className="font-semibold text-2xl text-slate-800 dark:text-white">
                        {results.breakEvenMonths} months
                      </div>
                    </div>
                    <div className="flex h-12 w-12 items-center justify-center rounded-full bg-slate-100 dark:bg-slate-800">
                      <TrendingUp className="h-6 w-6 text-green-600 dark:text-green-400" />
                    </div>
                  </div>
                </div>
              </CardFooter>
            </Card>

            <Card className="border border-slate-200 bg-white dark:border-slate-800 dark:bg-gray-900">
              <CardHeader>
                <CardTitle className="font-semibold text-lg">
                  Detailed Cost Breakdown
                </CardTitle>
                <CardDescription className="text-slate-500 dark:text-slate-400">
                  Comparing build and buy options over a 3-year period
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-4">
                    <h3 className="font-geist font-medium text-slate-800 dark:text-white">
                      Build Option
                    </h3>
                    <div className="grid gap-2">
                      <div className="flex justify-between">
                        <span className="text-slate-500 text-sm dark:text-slate-400">
                          Initial Build Cost:
                        </span>
                        <span className="font-medium text-slate-800 text-sm dark:text-white">
                          ${results.initialBuildCost.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-500 text-sm dark:text-slate-400">
                          Yearly Maintenance:
                        </span>
                        <span className="font-medium text-slate-800 text-sm dark:text-white">
                          ${results.yearlyMaintenanceCost.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-500 text-sm dark:text-slate-400">
                          Yearly Infrastructure:
                        </span>
                        <span className="font-medium text-slate-800 text-sm dark:text-white">
                          ${results.yearlyInfrastructureCost.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-500 text-sm dark:text-slate-400">
                          Monthly Per User:
                        </span>
                        <span className="font-medium text-slate-800 text-sm dark:text-white">
                          $
                          {results.monthlyPerUserBuildCost.toLocaleString(
                            undefined,
                            {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            }
                          )}
                        </span>
                      </div>
                      <div className="flex justify-between font-medium">
                        <span className="text-slate-800 text-sm dark:text-white">
                          3-Year Total Cost:
                        </span>
                        <span className="text-slate-800 text-sm dark:text-white">
                          ${results.threeYearBuildCost.toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="font-geist font-medium text-slate-800 dark:text-white">
                      SaaS Option
                    </h3>
                    <div className="grid gap-2">
                      <div className="flex justify-between">
                        <span className="text-slate-500 text-sm dark:text-slate-400">
                          Yearly Subscription:
                        </span>
                        <span className="font-medium text-slate-800 text-sm dark:text-white">
                          ${results.yearlySubscriptionCost.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-500 text-sm dark:text-slate-400">
                          Monthly Per User:
                        </span>
                        <span className="font-medium text-slate-800 text-sm dark:text-white">
                          $
                          {results.monthlyPerUserSaasCost.toLocaleString(
                            undefined,
                            {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            }
                          )}
                        </span>
                      </div>
                      <div className="flex justify-between font-medium">
                        <span className="text-slate-800 text-sm dark:text-white">
                          3-Year Total Cost:
                        </span>
                        <span className="text-slate-800 text-sm dark:text-white">
                          ${results.threeYearSaasCost.toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </section>
  );
}
