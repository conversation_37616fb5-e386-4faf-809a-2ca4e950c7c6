'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';
import {
  EventItem,
  InterviewItem,
  ProjectItem,
  WorkItem,
} from '@/components/sections';
import { events, inspirations, interviews, projects, workItems } from '@/data';
import AboutImg from '@/public/images/about-tomaslau.jpg';

export default function About() {
  const [showAllWork, setShowAllWork] = useState(false);
  const [showAllProjects, setShowAllProjects] = useState(false);
  const [showAllInspirations, setShowAllInspirations] = useState(false);
  const [showAllInterviews, setShowAllInterviews] = useState(false);
  const [showAllEvents, setShowAllEvents] = useState(false);

  const activeWorkItems = workItems.filter((item) => item.year.includes('Now'));
  const displayedWorkItems = showAllWork ? workItems : activeWorkItems;

  const activeProjects = projects.filter((item) => item.status === 'Active');
  const displayedProjects = showAllProjects ? projects : activeProjects;

  const displayedInspirations = showAllInspirations
    ? inspirations
    : inspirations.slice(0, 10);
  const displayedInterviews = showAllInterviews
    ? interviews
    : interviews.slice(0, 10);
  const displayedEvents = showAllEvents ? events : events.slice(0, 10);

  return (
    <div className="grow pt-12 pb-16 md:pt-16 md:pb-20">
      <div className="mx-auto max-w-[700px]">
        <section>
          {/* Page title */}
          <h1 className="mb-6 font-sans font-semibold text-2xl text-slate-800 dark:text-slate-100">
            About
          </h1>
          <Image
            alt="About"
            className="mb-6 w-full rounded-md shadow-sm"
            height={390}
            src={AboutImg}
            width={692}
          />
          {/* Page content */}
          <div className="space-y-8 text-slate-500 text-sm dark:text-slate-400">
            <div className="space-y-4">
              <p>
                Thank you so much for visiting my digital home. This blog is a
                collection of everything I do and find useful and interesting in
                life and work.
              </p>
              <p>
                From random thoughts and{' '}
                <Link
                  className="font-medium text-emerald-500 hover:underline"
                  href="/blog/questions"
                >
                  questions
                </Link>{' '}
                to my{' '}
                <Link
                  className="font-medium text-emerald-500 hover:underline"
                  href="/blog/favorites"
                >
                  favorites
                </Link>
                :{' '}
                <Link
                  className="font-medium text-emerald-500 hover:underline"
                  href="/blog/reading-list"
                >
                  books
                </Link>
                , articles, quotes, startups to watch or simply a screenshot of
                a smart email.
              </p>
              <h2 className="mb-4 font-sans font-semibold text-slate-800 text-xl dark:text-slate-100">
                Short Bio
              </h2>
              <div className="max-w-none">
                <p className="mb-4 text-slate-500 text-sm dark:text-slate-400">
                  I've written an ebook, created an online course, founded a
                  popular design magazine reaching 1+ million annual readers,
                  co-founded a content marketing agency, and have been published
                  in various well-known publications such as Forbes, TIME, Fast
                  Company, HuffPost, The Next Web, and more.
                </p>
                <ul className="list-inside list-disc space-y-1.5 text-slate-500 text-sm dark:text-slate-400">
                  <li>
                    I'm from Lithuania, now living with my wife Isabella in
                    Alicane, Spain
                  </li>
                  <li>Former digital nomad, traveled to 50+ countries</li>
                  <li>Studied in Aarhus, Denmark</li>
                  <li>My favorite book is Sapiens by Yuval Noah Harari</li>
                  <li>I speak Lithuanian, English and Spanish</li>
                  <li>
                    I buy way too many books than I can read. I have Scribd
                    subscription until 2351
                  </li>
                </ul>

                <div className="mt-6">
                  <p className="text-slate-500 text-sm dark:text-slate-400">
                    If you want to know more about my journey, start with these
                    long-form interviews on{' '}
                    <a
                      className="font-medium text-emerald-500 hover:underline"
                      href="https://www.typeform.com/blog/ask-awesomely/tomas-laurinavicius-interview/"
                      rel="noopener"
                      target="_blank"
                    >
                      Typeform
                    </a>{' '}
                    and{' '}
                    <a
                      className="font-medium text-emerald-500 hover:underline"
                      href="https://medium.com/crowdfire-product/veni-vidi-vici-a-tale-of-the-modern-day-conqueror-dd8c3c14bfcb"
                      rel="noopener"
                      target="_blank"
                    >
                      Crowdfire
                    </a>
                    .
                  </p>
                </div>
              </div>

              {/* Interests Section */}
              <div className="mt-8">
                <h2 className="mb-4 font-sans font-semibold text-slate-800 text-xl dark:text-slate-100">
                  Interests
                </h2>
                <div className="text-slate-500 text-sm dark:text-slate-400">
                  <p className="mb-4">
                    Generally, I'm glued to my iMac or MacBook screens all day
                    long learning, marketing, coding, writing, and designing.
                    I'm lucky to also get paid for it. While not working, I
                    spend my time:
                  </p>
                  <ul className="list-inside list-disc space-y-2">
                    <li>
                      <Link
                        className="text-slate-800 transition-colors hover:text-slate-900 dark:text-slate-200 dark:hover:text-white"
                        href="https://www.indiehackers.com/tomaslau"
                      >
                        Indie Hacking
                      </Link>
                      <span className="text-slate-500 dark:text-slate-400">
                        {' '}
                        — Building lean independent businesses
                      </span>
                    </li>
                    <li>
                      <Link
                        className="text-slate-800 transition-colors hover:text-slate-900 dark:text-slate-200 dark:hover:text-white"
                        href="https://tomaslau.com/"
                      >
                        Writing
                      </Link>
                      <span className="text-slate-500 dark:text-slate-400">
                        {' '}
                        — Blogging and self-publishing independent books
                      </span>
                    </li>
                    <li>
                      <Link
                        className="text-slate-800 transition-colors hover:text-slate-900 dark:text-slate-200 dark:hover:text-white"
                        href="https://www.goodreads.com/author/show/14358907.Tomas_Laurinavicius"
                      >
                        Reading
                      </Link>
                      <span className="text-slate-500 dark:text-slate-400">
                        {' '}
                        — Biographies, history, business,self-help, science,
                        psychology books
                      </span>
                    </li>
                    <li>
                      <Link
                        className="text-slate-800 transition-colors hover:text-slate-900 dark:text-slate-200 dark:hover:text-white"
                        href="https://stronglifts.com/app/"
                      >
                        Weightlifting
                      </Link>
                      <span className="text-slate-500 dark:text-slate-400">
                        {' '}
                        — StrongLifts 5×5 training
                      </span>
                    </li>
                    <li>
                      <Link
                        className="text-slate-800 transition-colors hover:text-slate-900 dark:text-slate-200 dark:hover:text-white"
                        href="https://tomaslau.com/"
                      >
                        Basketball
                      </Link>
                      <span className="text-slate-500 dark:text-slate-400">
                        {' '}
                        — Playing pickup games
                      </span>
                    </li>

                    <li>
                      <Link
                        className="text-slate-800 transition-colors hover:text-slate-900 dark:text-slate-200 dark:hover:text-white"
                        href="https://www.takeoverpod.com/"
                      >
                        History
                      </Link>
                      <span className="text-slate-500 dark:text-slate-400">
                        {' '}
                        — Studying through How to Take Over the World and
                        Founders podcasts
                      </span>
                    </li>
                    <li>
                      <Link
                        className="text-slate-800 transition-colors hover:text-slate-900 dark:text-slate-200 dark:hover:text-white"
                        href="https://peterattiamd.com/podcast/"
                      >
                        Longevity
                      </Link>
                      <span className="text-slate-500 dark:text-slate-400">
                        {' '}
                        — Learning about health optimization and life extension
                      </span>
                    </li>

                    <li>
                      <Link
                        className="text-slate-800 transition-colors hover:text-slate-900 dark:text-slate-200 dark:hover:text-white"
                        href="https://tomaslau.com/"
                      >
                        Self Development
                      </Link>
                      <span className="text-slate-500 dark:text-slate-400">
                        {' '}
                        — Continuous learning and personal growth
                      </span>
                    </li>

                    <li>
                      <Link
                        className="text-slate-800 transition-colors hover:text-slate-900 dark:text-slate-200 dark:hover:text-white"
                        href="https://nomads.com/@tomaslau"
                      >
                        Traveling
                      </Link>
                      <span className="text-slate-500 dark:text-slate-400">
                        {' '}
                        — 50+ countries
                      </span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <h2 className="mb-4 font-sans font-semibold text-slate-800 text-xl dark:text-slate-100">
                Career
              </h2>
              <p className="text-slate-500 text-sm dark:text-slate-400">
                My digital journey began in 2007 as a self-taught designer and
                writer.
              </p>
              <p className="text-slate-500 text-sm dark:text-slate-400">
                Since then, I've expanded my skills into marketing and SEO, and
                more recently, I've started learning programming to become a
                full-stack founder - someone who can build things quickly and
                independently.
              </p>
            </div>

            {/* Projects Section */}
            <div className="space-y-4">
              <h2 className="mb-4 font-sans font-semibold text-slate-800 text-xl dark:text-slate-100">
                Projects
              </h2>
              <div className="border-slate-200 border-t dark:border-slate-800">
                {displayedProjects.map((project) => (
                  <ProjectItem key={project.title} {...project} />
                ))}
                {projects.length > activeProjects.length && (
                  <div className="flex items-center justify-between py-2">
                    <button
                      className="rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] text-slate-500 transition-colors hover:bg-slate-200 dark:bg-slate-800 dark:text-slate-400 dark:hover:bg-slate-700"
                      onClick={() => setShowAllProjects(!showAllProjects)}
                      type="button"
                    >
                      {showAllProjects
                        ? `Show active (${activeProjects.length})`
                        : `Show all (${projects.length})`}
                    </button>
                  </div>
                )}
              </div>
            </div>
            <div className="space-y-4">
              <h2 className="mb-4 font-sans font-semibold text-slate-800 text-xl dark:text-slate-100">
                Work
              </h2>
              <div className="border-slate-200 border-t dark:border-slate-800">
                {displayedWorkItems.map((item) => (
                  <WorkItem key={item.title} {...item} />
                ))}
                {workItems.length > activeWorkItems.length && (
                  <div className="flex items-center justify-between py-2">
                    <button
                      className="rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] text-slate-500 transition-colors hover:bg-slate-200 dark:bg-slate-800 dark:text-slate-400 dark:hover:bg-slate-700"
                      onClick={() => setShowAllWork(!showAllWork)}
                      type="button"
                    >
                      {showAllWork
                        ? `Show active (${activeWorkItems.length})`
                        : `Show all (${workItems.length})`}
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* Interviews Section */}
            <div className="mt-8">
              <h2 className="mb-4 font-sans font-semibold text-slate-800 text-xl dark:text-slate-100">
                Interviews
              </h2>
              <div className="border-slate-200 border-t dark:border-slate-800">
                {displayedInterviews.map((interview) => (
                  <InterviewItem key={interview.title} {...interview} />
                ))}
                {interviews.length > 10 && (
                  <div className="flex items-center justify-between py-2">
                    <button
                      className="rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] text-slate-500 transition-colors hover:bg-slate-200 dark:bg-slate-800 dark:text-slate-400 dark:hover:bg-slate-700"
                      onClick={() => setShowAllInterviews(!showAllInterviews)}
                      type="button"
                    >
                      {showAllInterviews
                        ? 'Show less (10)'
                        : `Show all (${interviews.length})`}
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* Inspirations Section */}
            <div className="mt-8">
              <h2 className="mb-4 font-sans font-semibold text-slate-800 text-xl dark:text-slate-100">
                Inspirations
              </h2>
              <div className="border-slate-200 border-t dark:border-slate-800">
                {displayedInspirations.map((person) => (
                  <div
                    className="flex items-center justify-between py-2"
                    key={person.name}
                  >
                    <div className="flex items-center space-x-3">
                      <Link
                        className="text-slate-800 text-sm transition-colors hover:text-slate-900 dark:text-slate-200 dark:hover:text-white"
                        href={person.url}
                        rel="noopener"
                        target="_blank"
                      >
                        {person.name}
                      </Link>
                      <span className="text-slate-500 text-xs dark:text-slate-400">
                        {person.description}
                      </span>
                    </div>
                  </div>
                ))}
                {inspirations.length > 10 && (
                  <div className="flex items-center justify-between py-2">
                    <button
                      className="rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] text-slate-500 transition-colors hover:bg-slate-200 dark:bg-slate-800 dark:text-slate-400 dark:hover:bg-slate-700"
                      onClick={() =>
                        setShowAllInspirations(!showAllInspirations)
                      }
                      type="button"
                    >
                      {showAllInspirations
                        ? 'Show less (10)'
                        : `Show all (${inspirations.length})`}
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* Events Section */}
            <div className="mt-8">
              <h2 className="mb-4 font-sans font-semibold text-slate-800 text-xl dark:text-slate-100">
                Events
              </h2>
              <div className="border-slate-200 border-t dark:border-slate-800">
                {displayedEvents.map((item) => (
                  <EventItem key={item.title} {...item} />
                ))}
                {events.length > 10 && (
                  <div className="flex items-center justify-between py-2">
                    <button
                      className="rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] text-slate-500 transition-colors hover:bg-slate-200 dark:bg-slate-800 dark:text-slate-400 dark:hover:bg-slate-700"
                      onClick={() => setShowAllEvents(!showAllEvents)}
                      type="button"
                    >
                      {showAllEvents
                        ? 'Show less (10)'
                        : `Show all (${events.length})`}
                    </button>
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-4">
              <h2 className="mb-4 font-sans font-semibold text-slate-800 text-xl dark:text-slate-100">
                Why This Blog Exists?
              </h2>
              <p className="text-slate-500 text-sm dark:text-slate-400">
                Mainly for three selfish reasons.
              </p>
            </div>
            <div className="space-y-4">
              <h3 className="mb-2 font-sans font-semibold text-lg text-slate-800 dark:text-slate-100">
                1. Creative Outlet
              </h3>
              <p className="text-slate-500 text-sm dark:text-slate-400">
                I like to have a creative outlet without any boundaries.
              </p>

              <p className="text-slate-500 text-sm dark:text-slate-400">
                It's satisfying to make money from writing but that's the
                byproduct and it's one of the easiest ways to sell out and
                corrupt my work and values. I've tried monetizing with ads,
                affiliate links and subscriptions but none of them work for me
                and I've removed any commercial content from this blog.
              </p>
              <p className="text-slate-500 text-sm dark:text-slate-400">
                I make a living from other sources and want to be free of
                financial, political and social pressure and express myself on
                the topics that matter to me.
              </p>
              <p className="text-slate-500 text-sm dark:text-slate-400">
                It's my intrinsic motivation to write and create and get better
                at it without expecting to get anything in return.
              </p>
              <p className="text-slate-500 text-sm dark:text-slate-400">
                Writing and creating helps me organize my mind and make sense of
                what I'm thinking and better understand the surrounding world
                around me.
              </p>
              <p className="text-slate-500 text-sm dark:text-slate-400">
                I find writing therapeutic and creating in public a humbling
                experience.
              </p>
            </div>
            <div className="space-y-4">
              <h3 className="mb-2 font-sans font-semibold text-lg text-slate-800 dark:text-slate-100">
                2. Give Back and Pay Forward
              </h3>
              <p className="text-slate-500 text-sm dark:text-slate-400">
                I wouldn't be where I'm today if it wasn't for the men and women
                in the arena brave enough to share what they know.
              </p>
              <blockquote className="my-6 border-emerald-500 border-l-4 pl-4 text-slate-600 text-sm italic dark:text-slate-300">
                <p className="mb-4">
                  "It is not the critic who counts; not the man who points out
                  how the strong man stumbles, or where the doer of deeds could
                  have done them better.
                </p>
                <p className="mb-4">
                  The credit belongs to the man who is actually in the arena,
                  whose face is marred by dust and sweat and blood; who strives
                  valiantly; who errs, who comes short again and again, because
                  there is no effort without error and shortcoming;
                </p>
                <p className="mb-4">
                  But who does actually strive to do the deeds; who knows great
                  enthusiasms, the great devotions; who spends himself in a
                  worthy cause;
                </p>
                <p>
                  Who at the best knows in the end the triumph of high
                  achievement, and who at the worst, if he fails, at least fails
                  while daring greatly, so that his place shall never be with
                  those cold and timid souls who neither know victory nor
                  defeat."
                </p>
                <footer className="mt-4 font-medium text-slate-500 text-sm not-italic dark:text-slate-400">
                  — Theodore Roosevelt, <cite>The Man in the Arena</cite>
                </footer>
              </blockquote>
              <p className="text-slate-500 text-sm dark:text-slate-400">
                <strong>Standing on the shoulders of giants.</strong> I'm here
                because of all the writers, bloggers, artists, designers,
                teachers, developers, scientists, entrepreneurs, philosophers,
                and many people I can't possibly name that my life and work is
                based upon. This website is my way of paying it forward and
                sharing the ideas, best practices, and tools to create more and
                live better.
              </p>
            </div>

            <div className="space-y-4">
              <h3 className="mb-2 font-sans font-semibold text-lg text-slate-800 dark:text-slate-100">
                3. Attract and Connect
              </h3>
              <p className="text-slate-500 text-sm dark:text-slate-400">
                Writing is my online microphone.
              </p>

              <p className="text-slate-500 text-sm dark:text-slate-400">
                By writing online, I can share my ideas and expertise with
                people all over the world, even when I'm not actively working.
              </p>
              <p className="text-slate-500 text-sm dark:text-slate-400">
                It helps me build a reputation as a knowledgeable and thoughtful
                person in my field, and it allows me to connect with other
                curious people who share my interests. By writing, I've opened
                myself up to new opportunities, job offers, collaborations, and
                even lifelong friendships.
              </p>
            </div>
            <div className="space-y-4">
              <h2 className="mb-4 font-sans font-semibold text-slate-800 text-xl dark:text-slate-100">
                Most Popular Posts
              </h2>

              <p className="mb-4 text-slate-500 text-sm dark:text-slate-400">
                If you're completely new to my writing, here are some pieces I
                recommend you to start with.
              </p>

              <ul className="list-inside list-disc space-y-2 text-sm">
                <li>
                  <Link
                    className="text-slate-800 hover:text-emerald-500 dark:text-slate-200 dark:hover:text-emerald-500"
                    href="/blog/vipassana"
                  >
                    Lessons I Learned From 10 Days of Silence and Meditation
                  </Link>
                </li>
                <li>
                  <Link
                    className="text-slate-800 hover:text-emerald-500 dark:text-slate-200 dark:hover:text-emerald-500"
                    href="/blog/habits"
                  >
                    How to Steal the Habits of Successful People
                  </Link>
                </li>
                <li>
                  <Link
                    className="text-slate-800 hover:text-emerald-500 dark:text-slate-200 dark:hover:text-emerald-500"
                    href="/blog/discipline"
                  >
                    How to Develop Self-Discipline and Transform Your Life
                  </Link>
                </li>
                <li>
                  <Link
                    className="text-slate-800 hover:text-emerald-500 dark:text-slate-200 dark:hover:text-emerald-500"
                    href="/blog/college-alternatives"
                  >
                    Alternatives to College That Will Change Your Life
                  </Link>
                </li>
                <li>
                  <Link
                    className="text-slate-800 hover:text-emerald-500 dark:text-slate-200 dark:hover:text-emerald-500"
                    href="/blog/doubt"
                  >
                    Four Powerful Beliefs to Help You Overcome Self-Doubt
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-4">
              <h2 className="mb-4 font-sans font-semibold text-slate-800 text-xl dark:text-slate-100">
                Let's Connect
              </h2>

              <p className="text-slate-500 text-sm dark:text-slate-400">
                Don't follow me, connect with me.
              </p>
              <p className="text-slate-500 text-sm dark:text-slate-400">
                After deleting Twitter in 2018, I'm back on{' '}
                <a
                  className="font-medium text-emerald-500 hover:underline"
                  href="https://x.com/tomaslaucom"
                  rel="noopener"
                  target="_blank"
                >
                  X
                </a>{' '}
                and{' '}
                <a
                  className="font-medium text-emerald-500 hover:underline"
                  href="https://bsky.app/profile/tomaslau.com"
                  rel="noopener"
                  target="_blank"
                >
                  Bluesky
                </a>
                . If you're there, say hi!
              </p>
              <p className="text-slate-500 text-sm dark:text-slate-400">
                You can still find me on{' '}
                <a
                  className="font-medium text-emerald-500 hover:underline"
                  href="https://www.linkedin.com/in/tomaslau/"
                  rel="noopener"
                  target="_blank"
                >
                  LinkedIn
                </a>{' '}
                but the best way to stay in touch is by joining my{' '}
                <Link
                  className="font-medium text-emerald-500 hover:underline"
                  href="/newsletter"
                >
                  newsletter
                </Link>
                .
              </p>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
