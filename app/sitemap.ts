import type { MetadataRoute } from 'next';
import { getAllPosts } from '@/lib/mdx';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const posts = await getAllPosts();

  const blogPosts = posts.map((post) => ({
    url: `https://tomaslau.com/blog/${post.slug}`,
    lastModified: post.updatedAt || post.publishedAt,
  }));

  const routes = ['', '/about', '/blog', '/now'].map((route) => ({
    url: `https://tomaslau.com${route}`,
    lastModified: new Date().toISOString().split('T')[0],
  }));

  return [...routes, ...blogPosts];
}
