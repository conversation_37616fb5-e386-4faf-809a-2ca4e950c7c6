import Link from 'next/link';
import WidgetBook from '@/components/widget-book';
import WidgetSponsor from '@/components/widget-sponsor';

export const metadata = {
  title: "You're In! Welcome to Growthlog",
  description:
    "Thank you for confirming your subscription. You're now officially part of the Growthlog community.",
  alternates: {
    languages: {
      en: '/confirmed',
    },
  },
};

export default function Subscribe() {
  return (
    <div className="grow space-y-8 pt-12 pb-16 md:flex md:space-x-8 md:space-y-0 md:pt-16 md:pb-20">
      {/* Middle area */}
      <div className="grow">
        <div className="max-w-[700px]">
          <div className="space-y-10">
            <section>
              {/* Page title */}
              <h1 className="h1 mb-5 font-aspekta">
                You're In! Welcome to Growthlog 🎉
              </h1>
              {/* Page content */}
              <div className="space-y-8 text-slate-500 dark:text-slate-400">
                <p>
                  Thank you for confirming your subscription. You're now
                  officially part of the Growthlog community.
                </p>
                <p>
                  Every week I'll share updates on my projects, ideas from books
                  and blogs, and interesting tools and apps. While you're
                  waiting for my next newsletter,{' '}
                  <Link
                    className="font-medium text-emerald-500 hover:underline"
                    href="/about"
                  >
                    start here
                  </Link>{' '}
                  to learn more about me.
                </p>
                <p>
                  Feel free to{' '}
                  <a
                    className="font-medium text-emerald-500 hover:underline"
                    href="mailto:<EMAIL>"
                  >
                    reach out
                  </a>{' '}
                  to me anytime if you have questions, ideas, or just want to
                  say hello. Your feedback matters, and I'm here to listen.
                </p>
              </div>
            </section>
          </div>
        </div>
      </div>

      {/* Right sidebar */}
      <aside className="shrink-0 md:w-[240px] lg:w-[300px]">
        <div className="space-y-6">
          <WidgetSponsor />
          <WidgetBook />
        </div>
      </aside>
    </div>
  );
}
