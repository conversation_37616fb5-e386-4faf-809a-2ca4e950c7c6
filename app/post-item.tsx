import Link from 'next/link';
import { formatDate } from '@/utils/date-formatter';

export default function PostItem({ ...props }) {
  return (
    <article className="border-slate-100 border-b py-5 dark:border-slate-800">
      <div className="flex items-start">
        <div className="w-full">
          <div className="mb-1 text-slate-500 text-xs uppercase">
            <span className="text-emerald-500">—</span>{' '}
            {formatDate(props.publishedAt)}
          </div>
          <h3 className="mb-1 font-sans font-semibold text-lg">
            <Link
              aria-label={`Read ${props.title}`}
              className="before:-z-10 before:-rotate-2 relative inline-flex duration-150 ease-out before:absolute before:inset-0 before:origin-center before:translate-y-1/4 before:scale-x-0 before:bg-emerald-200 before:opacity-30 before:duration-150 before:ease-in-out hover:text-emerald-500 hover:before:scale-100 dark:before:bg-emerald-500"
              href={`blog/${props.slug}`}
            >
              {props.title}
            </Link>
          </h3>
          <div className="flex">
            <div className="grow text-slate-500 text-sm dark:text-slate-400">
              {props.summary}
            </div>
            <Link
              aria-label={`Read ${props.title}`}
              className="group hidden w-12 shrink-0 items-center justify-center text-emerald-500 lg:flex"
              href={`blog/${props.slug}`}
              tabIndex={-1}
            >
              <svg
                className="fill-current duration-150 ease-in-out group-hover:translate-x-2"
                height="12"
                width="14"
                xmlns="http://www.w3.org/2000/svg"
              >
                <title>Read article</title>
                <path d="M9.586 5 6.293 1.707 7.707.293 13.414 6l-5.707 5.707-1.414-1.414L9.586 7H0V5h9.586Z" />
              </svg>
            </Link>
          </div>
        </div>
      </div>
    </article>
  );
}
