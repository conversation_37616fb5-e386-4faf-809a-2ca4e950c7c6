'use client';

import { useEffect } from 'react';

export default function BlogError({
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Error logged by Next.js
  }, []);

  return (
    <div className="grow pt-8 pb-12 md:pt-16 md:pb-20">
      <div className="max-w-[700px]">
        <div className="my-16 text-center">
          <h2 className="mb-4 font-sans font-semibold text-slate-800 text-xl dark:text-slate-100">
            Unable to load blog posts
          </h2>
          <p className="mb-6 text-slate-500 text-sm dark:text-slate-400">
            Sorry, there was a problem loading the blog posts. Please try again
            later.
          </p>
          <button
            className="rounded-md bg-emerald-500 px-4 py-2 text-sm text-white transition-colors hover:bg-emerald-600"
            onClick={() => reset()}
            type="button"
          >
            Try again
          </button>
        </div>
      </div>
    </div>
  );
}
