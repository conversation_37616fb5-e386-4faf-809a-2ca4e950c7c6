import Link from 'next/link';
import { getAllPosts, type Post } from '@/lib/mdx';

export const metadata = {
  title: 'Blog - <PERSON>',
  description: 'Archive of all blog posts by <PERSON>.',
};

export default async function BlogPage() {
  const posts = await getAllPosts();

  // Group posts by year
  const postsByYear = posts.reduce(
    (acc, post) => {
      const year = new Date(post.publishedAt).getFullYear();
      if (!acc[year]) {
        acc[year] = [];
      }
      acc[year].push(post);
      return acc;
    },
    {} as Record<number, typeof posts>
  );

  // Get years in descending order
  const years = Object.keys(postsByYear)
    .map(Number)
    .sort((a, b) => b - a);

  return (
    <div className="grow pt-8 pb-12 md:pt-16 md:pb-20">
      <div className="max-w-[700px]">
        <div className="space-y-8 md:space-y-10">
          <section>
            <div className="mb-6 flex items-center justify-between md:mb-8">
              <h2 className="font-[650] font-aspekta text-lg text-slate-800 md:text-xl dark:text-slate-100">
                Blog
              </h2>
            </div>
            <div className="space-y-8 md:space-y-10">
              {years.map((year) => (
                <div key={year}>
                  <div className="mb-3 flex items-center gap-2 md:mb-4 md:gap-3">
                    <div className="flex shrink-0 items-center gap-1.5 md:gap-2">
                      <span className="font-medium text-slate-500 text-sm dark:text-slate-400">
                        {year}
                      </span>
                      <span className="text-slate-400 text-xs dark:text-slate-500">
                        ({postsByYear[year].length}{' '}
                        {postsByYear[year].length === 1 ? 'post' : 'posts'})
                      </span>
                    </div>
                    <div className="h-px flex-1 bg-slate-200 dark:bg-slate-800" />
                  </div>
                  <div className="space-y-1">
                    {postsByYear[year].map((post: Post) => (
                      <div
                        className="flex items-center justify-between py-2"
                        key={post.slug}
                      >
                        <Link
                          className="line-clamp-1 text-slate-800 text-sm transition-colors hover:text-slate-900 dark:text-slate-200 dark:hover:text-white"
                          href={`/blog/${post.slug}`}
                        >
                          {post.title}
                        </Link>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </section>
        </div>
      </div>
    </div>
  );
}
