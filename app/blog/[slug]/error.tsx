'use client';

import Link from 'next/link';
import { useEffect, useState } from 'react';

export default function BlogPostError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    // Check if the user is in development mode
    setIsAdmin(process.env.NODE_ENV === 'development');
  }, []);

  return (
    <div className="grow pt-6 pb-16 md:pt-8 md:pb-20">
      <div className="max-w-[700px]">
        <div className="my-16 text-center">
          <h2 className="mb-4 font-sans font-semibold text-slate-800 text-xl dark:text-slate-100">
            Unable to load blog post
          </h2>
          <p className="mb-6 text-slate-500 text-sm dark:text-slate-400">
            Sorry, there was a problem loading this content. This might be due
            to a temporary issue or the post may no longer be available.
          </p>

          {isAdmin && (
            <div className="mb-6 rounded-md border border-red-200 bg-red-50 p-4 text-left text-xs dark:border-red-800 dark:bg-red-900/20">
              <h3 className="mb-2 font-medium text-red-800 dark:text-red-400">
                Developer Error Details:
              </h3>
              <p className="mb-2 font-mono text-red-700 dark:text-red-300">
                {error.message}
              </p>
              {error.stack && (
                <details>
                  <summary className="mb-1 cursor-pointer text-red-600 dark:text-red-400">
                    Stack Trace
                  </summary>
                  <pre className="max-h-40 overflow-auto whitespace-pre-wrap rounded bg-red-100 p-2 text-[10px] text-red-600 dark:bg-red-900/30 dark:text-red-400">
                    {error.stack}
                  </pre>
                </details>
              )}
            </div>
          )}

          <div className="flex justify-center gap-4">
            <button
              className="rounded-md bg-emerald-500 px-4 py-2 text-sm text-white transition-colors hover:bg-emerald-600"
              onClick={() => reset()}
              type="button"
            >
              Try again
            </button>
            <Link
              className="rounded-md bg-slate-200 px-4 py-2 text-slate-700 text-sm transition-colors hover:bg-slate-300 dark:bg-slate-800 dark:text-slate-300 dark:hover:bg-slate-700"
              href="/blog"
            >
              Return to blog
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
