/// <reference types="next" />

import type { Metadata } from 'next';
import { notFound } from 'next/navigation';
import ClientMDX from '@/components/client-mdx';
import MDXContent from '@/components/mdx-content';
import PostHeader, { PostMetadata } from '@/components/post-header';
import ReplyByEmail from '@/components/reply-by-email';
import { getAllPosts, getPost } from '@/lib/mdx';

// Force static rendering for the page
export const dynamicParams = false;
export const revalidate = false;

export async function generateStaticParams() {
  const posts = await getAllPosts();
  return posts.map((post) => ({
    slug: post.slug,
  }));
}

interface PageProps {
  params: Promise<{ slug: string }>;
}

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata | undefined> {
  const { slug } = await params;
  const post = await getPost(slug);

  if (!post) {
    return;
  }

  const { title, summary: description } = post;

  return {
    title,
    description,
    alternates: {
      canonical: `/blog/${slug}`,
      languages: {
        en: `/blog/${slug}`,
      },
    },
    robots: {
      index: true,
      follow: true,
      noimageindex: false,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  };
}

export default async function SinglePost({ params }: PageProps) {
  const { slug } = await params;

  const post = await getPost(slug);

  if (!post) {
    notFound();
  }

  return (
    <div className="grow pt-6 pb-16 md:pt-8 md:pb-20">
      <div className="max-w-[700px]">
        <article>
          <PostHeader
            location={post.location ?? ''}
            publishedAt={post.publishedAt}
            title={post.title}
            updatedAt={post.updatedAt}
          />
          <div className="space-y-4 text-slate-500 text-sm dark:text-slate-400">
            <MDXContent>
              <ClientMDX source={post.content} />
            </MDXContent>
            <ReplyByEmail postTitle={post.title} />
          </div>
          <PostMetadata
            location={post.location ?? ''}
            publishedAt={post.publishedAt}
            updatedAt={post.updatedAt}
          />
        </article>
      </div>
    </div>
  );
}
