'use client';

import { useState } from 'react';
import { CourseItem, MustRereadItem } from '@/components/sections';
import { courses, mustRereadList } from '@/data';

export default function Bookmarks() {
  const [showAllCourses, setShowAllCourses] = useState(false);
  const [showAllMustReread, setShowAllMustReread] = useState(false);

  const displayedCourses = showAllCourses ? courses : courses.slice(0, 10);
  const displayedMustReread = showAllMustReread
    ? mustRereadList
    : mustRereadList.slice(0, 10);

  return (
    <div className="grow pt-12 pb-16 md:pt-16 md:pb-20">
      <div className="mx-auto max-w-[700px]">
        <section>
          <h1 className="mb-8 font-bold font-sans text-4xl text-slate-800 dark:text-slate-100">
            Bookmarks
          </h1>
          <p className="mb-8 text-base text-slate-500 dark:text-slate-400">
            A collection of courses, resources, and tools that I've found
            helpful in my journey.
          </p>

          {/* Must Reread Section */}
          <div className="mt-8">
            <h2 className="mb-4 font-sans font-semibold text-slate-800 text-xl dark:text-slate-100">
              Must Reread
            </h2>
            <div className="border-slate-200 border-t dark:border-slate-800">
              {displayedMustReread.map((item) => (
                <MustRereadItem key={item.title} {...item} />
              ))}
              {mustRereadList.length > 10 && (
                <div className="flex items-center justify-between py-2">
                  <button
                    className="rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] text-slate-500 transition-colors hover:bg-slate-200 dark:bg-slate-800 dark:text-slate-400 dark:hover:bg-slate-700"
                    onClick={() => setShowAllMustReread(!showAllMustReread)}
                    type="button"
                  >
                    {showAllMustReread
                      ? 'Show less (10)'
                      : `Show all (${mustRereadList.length})`}
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Courses Section */}
          <div className="mt-8">
            <h2 className="mb-4 font-sans font-semibold text-slate-800 text-xl dark:text-slate-100">
              Courses
            </h2>
            <div className="border-slate-200 border-t dark:border-slate-800">
              {displayedCourses.map((item) => (
                <CourseItem key={item.title} {...item} />
              ))}
              {courses.length > 10 && (
                <div className="flex items-center justify-between py-2">
                  <button
                    className="rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] text-slate-500 transition-colors hover:bg-slate-200 dark:bg-slate-800 dark:text-slate-400 dark:hover:bg-slate-700"
                    onClick={() => setShowAllCourses(!showAllCourses)}
                    type="button"
                  >
                    {showAllCourses
                      ? 'Show less (10)'
                      : `Show all (${courses.length})`}
                  </button>
                </div>
              )}
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
