import type { Metada<PERSON> } from 'next';

export const metadata: Metadata = {
  title: "What I'm Doing Now",
  description:
    "Here's what I'm up to right now. Projects, interests, reading, and listening lists.",
  alternates: {
    canonical: '/now',
    languages: {
      en: '/now',
    },
  },
  robots: {
    index: true,
    follow: true,
    noimageindex: false,
    'max-video-preview': -1,
    'max-image-preview': 'large',
    'max-snippet': -1,
  },
};
