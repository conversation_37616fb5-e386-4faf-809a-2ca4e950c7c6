'use client';

import { useState } from 'react';
import {
  BookItem,
  LearningItem,
  PodcastItem,
  ProjectItem,
  ToolItem,
  WorkItem,
} from '@/components/sections';
import {
  type Book,
  books,
  learningItems,
  podcasts,
  projects,
  type Tool,
  tools,
  workItems,
} from '@/data';

interface ItemWithTitle {
  title: string;
}

interface RenderItemsProps<T extends ItemWithTitle = ItemWithTitle> {
  items: T[];
  Component: React.ComponentType<T>;
  section: string;
}

const RenderItems = <T extends ItemWithTitle>({
  items,
  Component,
  section,
}: RenderItemsProps<T>) => {
  const [expandedSections, setExpandedSections] = useState<
    Record<string, boolean>
  >({});

  const isExpanded = expandedSections[section];
  const displayItems = isExpanded ? items : items.slice(0, 5);
  const hasMore = items.length > 5;

  return (
    <>
      {displayItems.map((item) => (
        <Component key={item.title} {...item} />
      ))}
      {hasMore && (
        <button
          className="mt-2 flex items-center rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] text-slate-500 transition-colors hover:bg-slate-200 dark:bg-slate-800 dark:text-slate-400 dark:hover:bg-slate-700"
          onClick={() =>
            setExpandedSections({
              ...expandedSections,
              [section]: !isExpanded,
            })
          }
          type="button"
        >
          {isExpanded ? 'Show less' : `Show all (${items.length})`}
        </button>
      )}
    </>
  );
};

export default function Now() {
  const [showAllWork, setShowAllWork] = useState(false);
  const [showAllProjects, setShowAllProjects] = useState(false);
  const [showAllBooks, _setShowAllBooks] = useState(false);
  const [showAllPodcasts, setShowAllPodcasts] = useState(false);
  const [showAllLearning, setShowAllLearning] = useState(false);

  const activeWorkItems = workItems.filter((item) => item.year.includes('Now'));
  const displayedWorkItems = showAllWork ? workItems : activeWorkItems;

  const activeProjects = projects.filter((item) => item.status === 'Active');
  const displayedProjects = showAllProjects ? projects : activeProjects;

  const recentBooks = books.slice(0, 5);
  const _displayedBooks = showAllBooks ? books : recentBooks;

  const recentPodcasts = podcasts.slice(0, 5);
  const displayedPodcasts = showAllPodcasts ? podcasts : recentPodcasts;

  const recentLearning = learningItems.slice(0, 5);
  const displayedLearning = showAllLearning ? learningItems : recentLearning;

  return (
    <div className="grow pt-6 pb-16 md:pt-8 md:pb-20">
      <div className="max-w-[700px]">
        {/* Header */}
        <div className="mb-8">
          <h1 className="mb-4 font-bold font-geist text-slate-800 text-xl leading-tight md:text-2xl dark:text-white">
            What I'm Doing Now
          </h1>
          <p className="text-slate-500 text-xs dark:text-slate-400">
            Inspired by{' '}
            <a
              className="text-emerald-500 hover:underline"
              href="https://nownownow.com/"
              rel="noopener"
              target="_blank"
            >
              Derek Sivers
            </a>
            . Last updated manually on March 21, 2025.
          </p>
        </div>

        <div className="space-y-16">
          {/* Introduction */}
          <div>
            <h2 className="mb-3 font-medium font-sans text-slate-500 text-xs dark:text-slate-400">
              Introduction
            </h2>
            <div className="border-slate-200 border-t py-3 dark:border-slate-800">
              <p className="text-slate-500 text-sm dark:text-slate-400">
                This year, I'm focusing on building a portfolio of small bets as
                a partner at{' '}
                <a
                  className="text-emerald-500 hover:underline"
                  href="https://craftled.com"
                  rel="noopener"
                  target="_blank"
                >
                  Craftled
                </a>
                .
              </p>
            </div>
          </div>

          {/* Work Section */}
          <div>
            <h2 className="mb-3 font-medium font-sans text-slate-500 text-xs dark:text-slate-400">
              Work
            </h2>
            <div className="border-slate-200 border-t dark:border-slate-800">
              {displayedWorkItems.map((item) => (
                <WorkItem key={item.title} {...item} />
              ))}
              {workItems.length > activeWorkItems.length && (
                <div className="flex items-center justify-between py-2">
                  <button
                    className="rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] text-slate-500 transition-colors hover:bg-slate-200 dark:bg-slate-800 dark:text-slate-400 dark:hover:bg-slate-700"
                    onClick={() => setShowAllWork(!showAllWork)}
                    type="button"
                  >
                    {showAllWork
                      ? `Show active (${activeWorkItems.length})`
                      : `Show all (${workItems.length})`}
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Projects Section */}
          <div>
            <h2 className="mb-3 font-medium font-sans text-slate-500 text-xs dark:text-slate-400">
              Projects
            </h2>
            <div className="border-slate-200 border-t dark:border-slate-800">
              {displayedProjects.map((item) => (
                <ProjectItem key={item.title} {...item} />
              ))}
              {projects.length > activeProjects.length && (
                <div className="flex items-center justify-between py-2">
                  <button
                    className="rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] text-slate-500 transition-colors hover:bg-slate-200 dark:bg-slate-800 dark:text-slate-400 dark:hover:bg-slate-700"
                    onClick={() => setShowAllProjects(!showAllProjects)}
                    type="button"
                  >
                    {showAllProjects
                      ? `Show active (${activeProjects.length})`
                      : `Show all (${projects.length})`}
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Learning Section */}
          <div>
            <h2 className="mb-3 font-medium font-sans text-slate-500 text-xs dark:text-slate-400">
              Learning
            </h2>
            <div className="border-slate-200 border-t dark:border-slate-800">
              {displayedLearning.map((item) => (
                <LearningItem key={item.title} {...item} />
              ))}
              {learningItems.length > recentLearning.length && (
                <div className="flex items-center justify-between py-2">
                  <button
                    className="rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] text-slate-500 transition-colors hover:bg-slate-200 dark:bg-slate-800 dark:text-slate-400 dark:hover:bg-slate-700"
                    onClick={() => setShowAllLearning(!showAllLearning)}
                    type="button"
                  >
                    {showAllLearning
                      ? `Show recent (${recentLearning.length})`
                      : `Show all (${learningItems.length})`}
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Podcasts Section */}
          <div>
            <h2 className="mb-3 font-medium font-sans text-slate-500 text-xs dark:text-slate-400">
              Podcasts
            </h2>
            <div className="border-slate-200 border-t dark:border-slate-800">
              {displayedPodcasts.map((item) => (
                <PodcastItem key={item.title} {...item} />
              ))}
              {podcasts.length > recentPodcasts.length && (
                <div className="flex items-center justify-between py-2">
                  <button
                    className="rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] text-slate-500 transition-colors hover:bg-slate-200 dark:bg-slate-800 dark:text-slate-400 dark:hover:bg-slate-700"
                    onClick={() => setShowAllPodcasts(!showAllPodcasts)}
                    type="button"
                  >
                    {showAllPodcasts
                      ? `Show recent (${recentPodcasts.length})`
                      : `Show all (${podcasts.length})`}
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Reading List Section */}
          <div>
            <h2 className="mb-3 font-medium font-sans text-slate-500 text-xs dark:text-slate-400">
              Reading List
            </h2>
            <div className="border-slate-200 border-t dark:border-slate-800">
              <RenderItems<Book>
                Component={BookItem}
                items={books}
                section="books"
              />
            </div>
          </div>

          {/* Tools Section */}
          <div>
            <h2 className="mb-3 font-medium font-sans text-slate-500 text-xs dark:text-slate-400">
              Tools
            </h2>
            <div className="border-slate-200 border-t dark:border-slate-800">
              <RenderItems<Tool>
                Component={ToolItem}
                items={tools}
                section="tools"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
