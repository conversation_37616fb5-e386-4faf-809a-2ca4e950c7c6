Before (Boring): You can make money being blue collar

After (Polarizing): The $130 Treadmill Mover story about paying someone $130 to move a treadmill for 30 mins

Before (Boring): Work-life balance is important
After (Polarizing): The "Having Less Ambition" story about leaving the "rat race" and having less ambition

<PERSON> was on the verge of eviction before he learned email marketing and became a 7-figure business owner

Formulas:
1. Use real, relatable stories instead of just stating facts
2. Hook people with an unexpected statement, leave them hanging to read more
3. Tell stories of real people/clients who achieved success through your method/product

---

AI prompts:

Here is a bland fact about [topic]: [insert fact]
Now turn it into a compelling short story that illustrates the fact in an unexpected, attention-grabbing way.

I need a short 2-3 paragraph story that drives home this lesson about [topic]: [insert lesson]
The story should be realistic, relatable and leave the reader wanting more details.

Take this testimonial quote praising our [product/service]: [insert quote]
Expand it into a more fleshed out 4-5 sentence story about the person's struggle and how [product/service] helped them overcome it.

Now rewrite the ending in a way that takes an extreme, uncompromising stance that will get people talking, even if 20% hate it.

The key is pushing the AI to turn dry facts, figures or testimonials into fuller narrative stories with realistic details that draw the reader in. Using prompts that ask for unexpected, attentiongrabbing or cliffhanger story angles can also make them more engaging.