import './css/style.css';
import { GeistMono } from 'geist/font/mono';
import { GeistSans } from 'geist/font/sans';
import type { Metadata } from 'next';
import Script from 'next/script';
import Navbar from '@/components/navbar';
import Theme from './theme-provider';

export const metadata: Metadata = {
  title: '<PERSON>',
  description: 'Personal website of <PERSON>',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <script
          // biome-ignore lint/security/noDangerouslySetInnerHtml: Theme switching script needs to run before hydration
          dangerouslySetInnerHTML={{
            __html: `
          try {
            if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
              document.documentElement.classList.add('dark')
            } else {
              document.documentElement.classList.remove('dark')
            }
          } catch (_) {}
        `,
          }}
        />
      </head>
      <body
        className={`${GeistSans.variable} ${GeistMono.variable} bg-white font-sans tracking-tight antialiased dark:bg-slate-900 dark:text-slate-200`}
      >
        <Theme>
          <Navbar />
          <div className="mx-auto max-w-[700px] px-4 pt-24 sm:px-6">
            {/* Main content */}
            <main>{children}</main>
          </div>
        </Theme>
        <Script
          data-website-id="e329eb44-c3cf-4b89-a806-2ae1dc2f5cef"
          defer
          src="https://umami.craftled.com/script.js"
        />
      </body>
    </html>
  );
}
