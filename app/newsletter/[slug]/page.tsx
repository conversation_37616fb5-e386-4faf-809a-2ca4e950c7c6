import type { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { compileMDX } from 'next-mdx-remote/rsc';
import EnchargeWidget from '@/components/encharge-widget';
import MDXContent from '@/components/mdx-content';
import PostHeader, { PostMetadata } from '@/components/post-header';
import WidgetPosts from '@/components/widget-posts';
import WidgetSponsor from '@/components/widget-sponsor';
import { getAllPosts, getPost } from '@/lib/mdx';

export async function generateStaticParams() {
  const posts = await getAllPosts();
  return posts.map((post) => ({
    slug: post.slug,
  }));
}

interface GenerateMetadataProps {
  params: Promise<{ slug: string }>;
}

export async function generateMetadata({
  params,
}: GenerateMetadataProps): Promise<Metadata | undefined> {
  const { slug } = await params;
  const post = await getPost(slug);

  if (!post) {
    return;
  }

  const { title, summary: description } = post;

  return {
    title,
    description,
    alternates: {
      canonical: `/newsletter/${slug}`,
      languages: {
        en: `/newsletter/${slug}`,
      },
    },
    robots: {
      index: true,
      follow: true,
      noimageindex: false,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  };
}

export default async function SinglePost({ params }: GenerateMetadataProps) {
  const { slug } = await params;
  const post = await getPost(slug);

  if (!post) {
    notFound();
  }

  const { content } = await compileMDX({
    source: post.content,
    options: { parseFrontmatter: false },
  });

  return (
    <div className="grow pt-6 pb-16 md:pt-8 md:pb-20">
      <div className="max-w-[700px]">
        <article>
          <PostHeader
            location={post.location ?? ''}
            publishedAt={post.publishedAt}
            title={post.title}
            updatedAt={post.updatedAt}
          />
          <div className="space-y-4 text-slate-500 text-sm dark:text-slate-400">
            <MDXContent>{content}</MDXContent>
          </div>
          <PostMetadata
            location={post.location ?? ''}
            publishedAt={post.publishedAt}
            updatedAt={post.updatedAt}
          />
        </article>
        <div className="mt-8 space-y-8">
          <EnchargeWidget />
          <WidgetSponsor />
          <WidgetPosts />
        </div>
      </div>
    </div>
  );
}
