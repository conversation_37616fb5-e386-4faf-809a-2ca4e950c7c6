---
title: "My Favorite Stuff for Work and Play"
publishedAt: "2023-07-03"
updatedAt: "2024-01-22"
image: "/images/favorites.jpg"
location: "Alicante, Spain"
summary: "My collection of essential apps and tools for productivity and simplifying life, from efficient lifting with StrongLifts to privacy-focused browsing with Brave."
---

Some of my favorite apps and tools I use in life and work, listed in no particular order:

- [<PERSON>ursor](https://cursor.sh/): The best AI-first code editor for building things quickly.
- [StrongLifts](https://stronglifts.com/): The best lifting app I've ever used, simple and effective, based on the 5x5 strength program.
- [Brave](https://brave.com/): A fast and privacy-focused Chrome alternative.
- [Ahrefs](https://ahrefs.com/): My go-to tool for keyword research, market analysis, and competitor research.
- [1Password](https://1password.com/): A powerful and intuitive password manager for securely storing and sharing sensitive data.
- [Signal](https://signal.org): A privacy-focused messenger app highly recommended by <PERSON><PERSON>.
- [Framer](https://www.framer.com/): A fast and visually stunning website builder.
- [Notion](https://www.notion.so/): The all-in-one app for wikis, documentation, and project management.
- [Gmail](https://mail.google.com/): After trying various options, I found Gmail to be the best with its strong spam filters and fast interface.
- [CleanShot X](https://cleanshot.com/): Capture your Mac's screen like a pro.
- [Google Drive](https://drive.google.com/): An affordable and user-friendly file storage solution.
- [Figma](https://www.figma.com/): A super-fast and powerful interface design tool with a large community and extensive template library. I rarely start from scratch.
- [Raycast](https://www.raycast.com/): Replaces Mac's spotlight search with powerful tools and apps. I switched from TextExpander and Alfred.
- [Slack](https://slack.com/): An intuitive and enjoyable chat app.
- [Spotify](https://open.spotify.com/): My daily companion for listening to music and podcasts while working, exercising, or running errands.
- [ChatGPT](https://chat.openai.com/): It quickly replaced Google for me for generating ideas and troubleshooting code.
- [Phind](https://www.phind.com): AI search engine for developers with real-time browsing and code generation capabilities.

To be continued...
