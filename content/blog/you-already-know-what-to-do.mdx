---
title: "You Already Know What to Do"
publishedAt: "2025-07-06"
location: "Alicante, Spain"
summary: "You already know what to do, you just need to do it. Trust your instincts."
---

Most of us don’t need another how-to thread. We need to **do the thing we’ve rehearsed in our heads for eighteen months**.

Deep down, you already know the next step. Yet you stall, calling it _research_, _planning week_, or _one more course_. Underneath sit two silent fears:

1. **Fear of failure** – the ego hit if the launch flops.
2. **Fear of success** – the new expectations if it doesn’t.

So you buy another book, open another Notion doc, and stay perfectly safe by never shipping.

## The Missing Variable: Leverage

```
Output = Effort × Leverage
```

- **Effort** is capped at 24 h per day.
- **Raw intelligence** is capped by genetics.
- **Leverage** scales both.

If you are grinding twelve hours and still crawling, the constraint is not hustle, it is leverage:

| Leverage Type     | Multiplier         | Example (one-liner)                     |
| ----------------- | ------------------ | --------------------------------------- |
| **Code + Media**  | Infinite repeats   | Ship once, serve millions, online tools |
| **Capital**       | Money hiring money | Ad arbitrage, equity stakes             |
| **People**        | Other brains       | Delegated ops, agency model             |
| **Network/Brand** | Trust on tap       | “Published in Forbes” opens cold doors  |

Work on the part that keeps compounding while you sleep.

## Mine Your Existing Assets

Forget the blank slate, **reuse what is already on your hard drive**.

| Asset                 | Hidden Leverage   | Quick Move                                          |
| --------------------- | ----------------- | --------------------------------------------------- |
| Archive blog posts    | Authority and SEO | Bundle into a free e-book to capture emails         |
| Dormant newsletter    | Distribution      | Relaunch with a five-email “build in public” series |
| Old consulting slides | Product seed      | Convert into a 49-euro micro-course                 |
| Past podcast guests   | Network           | Ask each for a tweet at launch                      |

Every new project starts with one question: **Which asset makes this unfairly easy?**

## A Micro-Playbook to Get Moving

1. **One-page spec.** Force clarity, scope creep hates paper.
2. **Single leverage hook.** Audience, tiny script, friend’s Rolodex, pick one.
3. **Ship V0 in 48 hours.** Ugly beats imaginary, feedback is leverage you cannot buy.
4. **Iterate where upside compounds.** Add only what widens leverage, not what soothes perfectionism.

> Take a simple idea and take it seriously. – **Charles Munger**

You already know the simple idea: build, publish, learn. The serious part is rigging the game so each hour pushes on a bigger lever. Do that, and fear, whether of failure or success, gets crowded out by basic arithmetic. Now close this tab and go pull a lever.
