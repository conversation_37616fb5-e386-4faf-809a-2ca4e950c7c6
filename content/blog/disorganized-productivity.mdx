---
title: "Creative and Disorganized Productivity for Inevitable Success"
publishedAt: "2025-07-06"
location: "Alicante, Spain"
summary: ""
---

You probably didn't expect disorganized and productive to be in the same sentence, but here it is.

Most great work is a scribble that only looks like a straight line in hindsight.

- <PERSON> painted, [cracked safes](https://www.openculture.com/2013/04/learn_how_richard_feyn<PERSON>_cracked_the_safes_with_atomic_secrets_at_los_alamos.html) and rewrote quantum mechanics.
- <PERSON> kept the idea for _Inception_ in a notebook for [close to ten years before filming](https://gointothestory.blcklst.com/written-interview-christopher-nolan-b2b364b60f).
- <PERSON> wrote an _Avatar_ treatment in 1994 and waited fifteen years [until the tech caught up](https://screenrant.com/avatar-movie-development-long-reason/).
- <PERSON> [built 5,127 failed vacuums](https://www.jamesdysonfoundation.co.uk/who-we-are/our-story/) before one finally worked.
- <PERSON> told graduates that you can only [connect the dots looking backward](https://news.stanford.edu/stories/2005/06/youve-got-find-love-jobs-says), so you have to trust the path while it is still messy.

Their stories prove that progress can be creative, unconstrained and apparently disorganized yet still lead to long-term victory.

## Own your weird way of learning

Feynman said you have no responsibility to live up to what other people think you ought to accomplish. It is their mistake, not your failing.

He learned physics while beating bongos and sketching in bars. The method looked undisciplined, yet the knowledge stuck.

## Trust that the dots will connect

Jobs urged students to trust in something because believing the dots will connect later gives you the confidence to follow your heart now.

<PERSON> did exactly that. He worked on _Inception_ off and on for nearly a decade until he felt ready to film a dream-heist on a blockbuster scale.

Cameron finished the _Avatar_ script then shelved it until performance capture and CGI could build Pandora.

Each kept moving forward in small bursts, letting time do some of the heavy lifting.

## Work with your own rhythm

I build online products the same way.

One day I tweak a design, another day I write copy, another I revise onboarding. When I feel friction, I switch tracks instead of forcing output on demand. The shuffle keeps me productive, gives me fresh insights and prevents burnout.

Dyson’s five-year slog through thousands of prototypes shows why this matters. Every failed cyclone taught him something that a tidy plan would have missed.

**Keep a single guiding goal**. Write one clear sentence that defines victory. It keeps random tasks aligned.

**Rotate tasks when resistance hits**. Stuck with design? Write. Writer's block? Code. Coding problem? Sketch. Motion beats stagnation.

**Store every fragment**. A central vault for half ideas lets today’s scrap solve tomorrow’s block.

**Ship small visible pieces**. Post a Loom demo or tweet a screenshot. Public checkpoints turn chaos into feedback loops.

Design in the morning, outline at lunch, polish copy at midnight.

It may look disorganized, yet the output graph keeps rising. Trust the scribbles. They have led Nobel laureates, billion-dollar directors and vacuum-cleaner inventors to the finish line. They will work for you too.

So you do you! Have fun.
