---
title: "How to Set Up Favicon for Your Next.js Project"
publishedAt: "2023-07-02"
image: "/images/nextjs-favicon.jpg"
location: "Alicante, Spain"
summary: "Learn how to set up a favicon for your Next.js project using a complete favicon generator in Figma."
---

Here are some useful resources if you're trying to set up a favicon for your Next.js project.

- [Complete Favicon Generator](https://www.figma.com/community/file/914233657397286062): In the end, I used this generator in Figma. Thank you, [<PERSON>](https://www.figma.com/@ernest)!

- [Metadata Files API Reference (favicon, icon, and apple-icon)](https://nextjs.org/docs/app/api-reference/file-conventions/metadata/app-icons) from the official Next.js documentation. I found it not very clear and spent time asking [Phind](https://www.phind.com/) to help me find a solution.

- [RealFaviconGenerator](https://realfavicongenerator.net/): It promises to generate perfect favicon files in 5 minutes. It's doing a good job, but many files were missing.

- [Favicon checker](https://realfavicongenerator.net/favicon_checker): Use this tool to check if everything is working properly. It has some requirements, and I deployed without satisfying all of them.

Here's my working code snippet:

```tsx
export const metadata: Metadata = {
  title: "Growthlog by Tomas Laurinavicius",
  description:
    "Welcome to Growthlog by Tomas Laurinavicius, a founder, marketer, designer, and writer from Lithuania, based in Spain.",
  manifest: "/manifest.json",
  icons: {
    shortcut: { url: "/favicon.ico", type: "image/x-icon" },
    icon: [
      { url: "/favicon.ico", type: "image/x-icon" },
      { url: "/favicon.svg", type: "image/svg+xml" },
      { url: "/favicon-16x16.png", type: "image/png", sizes: "16x16" },
      { url: "/favicon-32x32.png", type: "image/png", sizes: "32x32" },
      { url: "/favicon-96x96.png", type: "image/png", sizes: "96x96" },
      {
        url: "/favicon-192x192.png",
        type: "image/png",
        sizes: "192x192",
      },
    ],
    apple: [
      { url: "/favicon-57x57.png", sizes: "57x57", type: "image/png" },
      { url: "/favicon-60x60.png", sizes: "60x60", type: "image/png" },
      { url: "/favicon-72x72.png", sizes: "72x72", type: "image/png" },
      { url: "/favicon-76x76.png", sizes: "76x76", type: "image/png" },
      {
        url: "/favicon-114x114.png",
        sizes: "114x114",
        type: "image/png",
      },
      {
        url: "/favicon-120x120.png",
        sizes: "120x120",
        type: "image/png",
      },
      {
        url: "/favicon-144x144.png",
        sizes: "144x144",
        type: "image/png",
      },
      {
        url: "/favicon-152x152.png",
        sizes: "152x152",
        type: "image/png",
      },
      {
        url: "/favicon-180x180.png",
        sizes: "180x180",
        type: "image/png",
      },
    ],
  },
};
```
