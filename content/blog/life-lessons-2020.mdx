---
title: "Life Lessons I Learned in 2020"
publishedAt: "2021-01-01"
image: "/images/life-lessons-2020.jpg"
location: "Vilnius, Lithuania"
summary: "I'm sipping red tea and finishing my birthday cake while reviewing my calendar and photos from 2020. What was that?"
---

Annual review of 2020. A year like no other.

![Tulum, Mexico](/images/tulum-mexico-tomaslau-2020.jpg)

Tulum, Mexico.

*“There are decades where nothing happens; and there are weeks where decades happen.”* – <PERSON>

This century-old quote perfectly sums up the weeks when the pandemic became uncontrollable and the World Health Organization announced it a global pandemic. I was on my way from Mexico to Lithuania and together with my wife we safely returned before the government decided to seal the country and close the borders. Then the weeks Lenin refers to happened. Stocks plummeted, crypto nosedived, oil was trading at negative prices, and everyone was freaking out. Remote work overnight became the norm and the present of work.

The year is over but the events that started in 2020 continue.

I'm sipping red tea and finishing my birthday cake while reviewing my calendar and photos from 2020. What was that?

It's been another year on this planet and oh boy what a colorful human experience can be.

I've been writing my annual review since 2013 and now is the time to wrap up 2020 – a year like no other (and no more please).

Today, I’d like to review my year and share the highlights, failures, successes, and biggest life lessons of 2020. It's been a crazy wild ride and I'm grateful to still be here publishing my thoughts to you my internet friend.

☕️ *This post contains partner links earning me commissions if you buy something at no extra cost to you.*

## Highlights

You only value things you don't have. You only understand how much you love the ordinary and boring when really bad things happen. 2020 was a year that we may never forget but many good things happened as well.

![Chichen Itza, Mexico](/images/chichen-itza-mexico-tomaslau-2020.jpg)

Chichen Itza, Mexico.

![Exploring Cozumel, Mexico](/images/exploring-cozumel-mexico-tomaslau-2020.jpg)

Exploring Cozumel, Mexico.

![Quad biking in Cusco, Peru](/images/quad-biking-in-cusco-peru-tomaslau-2020.jpg)

Quad biking in Cusco, Peru.

![My best friend's wedding. Vilnius, Lithuania](/images/my-best-friends-wedding-vilnius-lithuania-2020.jpg)

My best friend's wedding. Vilnius, Lithuania.

![After playing paintball during the bachelor party. Lithuania](/images/after-playing-paintball-during-the-bachelor-party-lithuania-2020.jpg)

After playing paintball during the bachelor party. Lithuania.

![Vilnius old town, Lithuania](/images/vilnius-old-town-lithuania-2020.jpg)

Vilnius old town, Lithuania.

Here are the highlights of 2020:

- Celebrated New Year with my wife's family in Santiago, Chile.
- Explored Lima, Peru enjoying one of the best cuisines in the world.
- Visited Machu Picchu ⛰ for the second time.
- Explored the magnificent countryside of Cusco on a quad bike.
- Moved my blog to **[Ghost](https://ghost.org/)**.
- Visited Chichen Itza, one of the Seven Wonders of the World.
- Took a ferry to Cozumel island, rented a Jeep, and drove around the island exploring, sunbathing, and snorkeling in lagoons.
- Joined a fun BBQ party on a rooftop organized by **[Adventure In You](https://www.adventureinyou.com/)**.
- Swimmed in freshwater cenotes in Tulum, Mexico.
- Went on a party boat tour to Isla Mujeres and had a blast.
- Started aggressively investing in the stock market. My tech-heavy portfolio is **+37.55%** for the year.
- Celebrated our first wedding anniversary.
- Stayed with my brother and had a chance to reconnect and bond playing FIFA, working on some projects together, and playing basketball.
- Launched **[Content Writing Jobs](https://contentwritingjobs.com/)** with **[Edgaras Benediktavicius](https://edgaras.com/)**.
- Rented a beautiful apartment in a great location in Vilnius that we can now call home.
- Hit the gym and played basketball 2 times a week before the quarantine restrictions.
- Worked with some great organizations including Product Hunt, BigCommerce, Webflow, and Envato.
- Visited my parents multiple times and shared some great time.
- Celebrated my grandma's 70th birthday.
- Had a crazy fun bachelor party before my best friend's wedding.
- Celebrated **[Isabella's 22nd birthday](https://www.instagram.com/isa_russi10/)**.
- Attended my best friend's special wedding event.
- Celebrated my dad's 50th birthday.
- Flew to Warsaw to attend Isabella's cousin's wedding.
- Went fishing with my wife (only caught one little fish).
- Launched Cleantechy over the weekend.
- Played Secret Santa and exchanged gifts with my family.

## Failures

This year was tense mentally and emotionally. Trying to control things that can't be controlled took a toll on my mental health.

While I don't see failures as an inherently bad thing, I think it's important to reflect on what didn't work and try to understand why so you can change the approach to doing things or realize that it's time to move on and try something new.

In 2020, I failed to:

- Keep my work/life balance.
- Avoid burn out.
- Manage my energy.
- Juggle client work and personal projects.
- Outsource and delegate.
- Find time to just sit in silence and breathe.

## Successes

Despite the global unrest and personal failures, 2020 has been one of the most successful years of my life. This year I:

- Made over six figures in personal income. Financially it was the most successful year and I'm happy to be working with some great organizations and professionals while having time for my side projects.
- Saved more than 60% of my income.
- Became a **[Makerpad](https://makerpad.zapier.com/)** member and learned building software solutions without coding.
- Learned a lot reading **[Trends](https://trends.co/)** reports analyzing rising industries.
- Learned UX/UI and switched from Photoshop to **[Figma](https://figma.com/)**.
- Migrated most of my websites to **[Webflow](https://webflow.com/)**.
- Learned automation using **[Zapier](https://zapier.com/)** and **[Integromat](https://integromat.com/)**.
- Spent a lot more time with my family.
- Exercised quite regularly (gym, basketball, HIIT).
- Practiced intermittent fasting.
- Visited 5 countries: Chile, Peru, Mexico, Poland, and Lithuania.
- Improved my spoken Spanish.
- Launched my first iOS app **[Secret Santa Friend](https://apps.apple.com/us/app/secret-santa-friend/id1540482599)** together with my brother.
- Spent quality time with friends.

## Life Lessons of 2020

Here’s what I learned in this overwhelming year.

**Crisis teaches you to be resourceful and responsible.** You learn to hack things yourself. You save your precious resources such as time and capital. All of that helps you to become more **[antifragile](https://taylorpearson.me/planning/)** that will serve you in good and bad times to come.

**Lower the downside, exploit the upside.** Protect the essentials while maximizing your potential returns.

**Personalization is everything.** Whether it's business, entertainment, diet, or the ultimate life experience, it all comes down to personalization. Big tech is doing it for you but probably not the way you want it, you can personalize your life to fit your preferences.

**Email people randomly.** Sending a **[cold email](https://hunter.io/templates/)** can do wonders. If you have been following someone's blog or YouTube channel or business, reach out and let them know. This way I connected with some really interesting people.

**Create.** Write, record, paint, draw, design, develop, speak. Keep creating even if it is for just one person. It's well worth it.

**Think in systems.** It's the ultimate leverage. You may have 24 hours in a day or 9 million hours in a day like Jeff Bezos (Amazon has 1,225,300 employees, assuming they work 8 hours a day we get over 9 million hours).

**History can't predict the future.** But we can study patterns and identify similar events happening that may help us adapt faster.

**Winning is not about being the strongest but surviving the longest.** It's not the strongest who survives but the one who adapts. Avoiding death pays better dividends than risking boldly.

**Exploit tech.** Technology is amazing unless it works against you. Personalize technology to fit your needs and learn automation. Use it as leverage to replace, clone, and amplify yourself. From startups to large enterprises to solo founders, the new generation of successful entrepreneurs knows how to make tech work for them.

**Quality thinking is a skill and you must develop it.** You have to create space and time for it. Quality work doesn't happen under pressure or with set outcomes in mind.

**Use your natural strengths.** The things you do when no one watches you, the things that make you comfortable is your unfair advantage. Do more of these, if you can't find a sustainable way to keep doing it.

**Explore a lot before you commit to something.** Just because you're good at something, it doesn't mean you have to commit right away. Whether it's your partner, business, career, diet, whatever, you have to explore first to understand what options you have before burning the bridges and deciding on the spot.

**Doing nothing is still a decision.** Even if you decide to sit and ignore the world, it's a decision you have to own and accept the consequences.

**Curious people are more interesting.** IQ is not everything as logic and your so-called “expertise” will stop you from exploring fearlessly. Curiosity leads to serendipity.

**Timing matters.** If you feel you have information that others may not have and you have the ability to execute, do it. If you wait and only wonder about it, you'll have to regret not taking action.

## Favorite Purchases of 2020

This year I invested in better software and tech that improved my productivity and lifestyle.

- **[Apple 27" iMac](https://www.amazon.com/imac-27/s?k=imac+27)**
- **[Bose 700](https://amzn.to/3n4wl7w)**
- **[Apple Watch 6](https://amzn.to/38OIrwp)**
- **[Apple iPhone 12 Pro](https://amzn.to/38OZQoF)**
- **[Withings Body Cardio](https://amzn.to/34ZFQyx)**
- **[Coda](https://coda.io/)**
- **[Fitbod](http://apple.co/fitbod)**
- **[Airtable](https://www.airtable.com/)**
- **[ConvertBox](https://convertbox.com/)**
- **[Webflow](https://webflow.com/)**
- **[SendFox](https://sendfox.com/)**
- **[Makerpad](https://makerpad.zapier.com/)**
- **[ClickMinded](https://www.clickminded.com/)**
- **[Scribd](https://www.scribd.com/)**
- **[YNAB](https://www.ynab.com/)**
- **[1Password](https://1password.com/)**
- **[Pipedrive](https://www.pipedrive.com/)**
- **[UI8](https://ui8.net/)**

## Favorite Books of 2020

- **[Optionality](https://amzn.to/38fXrDd)** by Richard Meadows
- **[The Psychology of Money](https://amzn.to/2WqMs4l)** by Morgan Housel
- **[Thinking in Bets](https://amzn.to/37rGHK7)** by Annie Duke
- **[The Courage to Be Disliked](https://amzn.to/3oYyF15)** by Ichiro Kishimi & Fumitake Koga
- **[Finish Big](https://amzn.to/38cszn1)** by Bo Burlingham
- **[Invent and Wander](https://amzn.to/38QK4cW)** by Walter Isaacson & Jeff Bezos
- **[How to Fail at Almost Everything and Still Win Big](https://amzn.to/384xvvl)** by Scott Adams
- **[The Almanack of Naval Ravikant](https://amzn.to/2L8Dg2e)** by Eric Jorgenson

It's quite therapeutic to reflect and review everything that happened in one year. While some things look important or not at all in the moment, they carry a different meaning in the hindsight.

2020 was a year like no other for most humans. While it was horrible in many ways, it's still just one year in your life. You can choose courage to live to be curious and take ownership of your life and live a grand life with some unavoidable failures that may not even end up on your life review.

Thank you for reading. Stay safe.

If you are interested in my previous annual reviews, check my life lessons of **[2013](/blog/life-lessons-2013)**, **[2014](/blog/life-lessons-2014)**, **[2015,](/blog/life-lessons-2015)** **[2016,](/blog/life-lessons-2016)** **[2017](/blog/life-lessons-2017)**, **[2018](/blog/life-lessons-2018)**, and **[2019](/blog/life-lessons-2019)**.
