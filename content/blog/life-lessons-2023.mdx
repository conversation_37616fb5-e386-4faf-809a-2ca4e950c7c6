---
title: "Life Lessons I Learned in 2023"
publishedAt: "2024-01-01"
image: "/images/life-lessons-2023.jpg"
location: "Alicante, Spain"
summary: "A year-end reflection on the highlights, challenges, favorite purchases, and impactful books of 2023."
---

I’m back and this is my annual review of 2023.

I skipped the last two years of annual reviews. Maybe I was busy, felt it wasn't needed anymore, or I was getting bitter with the Internet and myself.

Last year, my buddy <PERSON><PERSON> shared his [yearly reflections](https://edgaras.com/yearly-reflections-2022), and I was inspired to return to this practice, mostly because it forces me to review my calendar, and photos and reflect on the whole year.

This process always brings a smile to my face and fills me with gratitude for all the experiences I've had.

Another significant motivation for returning to this practice is the opportunity to revisit my own thoughts and lessons from five years ago.

It's rewarding to see my personal growth, to reflect on my past naivety, and to realize how much my understanding of the world and myself has evolved.

![Craftled Partners Workation 2023](/images/workation-2023.jpg)
A productive workation with my [Craftled partners](https://craftled.com/about), who also happen to be my wife, <PERSON>, and my best friend, <PERSON><PERSON>.

![Indonesian Vacation 2023](/images/vacation-2023.jpg)

Savoring the local cuisine, enjoying refreshing drinks, and witnessing breathtaking sunsets during our vacation in Lombok and Bali, Indonesia.

![UAE 2023](/images/uae-2023.jpg)
Exploring the Sheikh Zayed Grand Mosque in Abu Dhabi, tasting a unique camel burger, and enjoying shisha in front of the world's tallest building, the Burj Khalifa, in Dubai, United Arab Emirates.

![Stopovers 2023](/images/travels-2023.jpg)
We enjoyed brief, but memorable stopovers in Bulgaria and Singapore, where we ate lots of local food.

![saas.grouping 2023 in Barcelona](/images/saasgrouping-2023.jpg)
Attending the saas.grouping event in Barcelona, Spain.

![Go-Kart Racing 2023](/images/karts-2023.jpg)
Exhilarating go-kart racing experience in Elche, Spain.

![Proposal and Family Reunion 2023](/images/proposal-2023.jpg)
The much-anticipated proposal to my wife, Isabella, and a heartwarming meeting with our mothers in Lithuania, with Isabella's mother making the journey all the way from Chile.
![Indonesia 2023](/images/indonesia-2023.jpg)
We had fun swimming with dolphins in Lovina, enjoyed the different tastes of Indonesian food, tasted the local coffee, and had many relaxing massages.

![Memorable Moments of 2023](/images/fun-2023.jpg)
A collection of memorable moments from 2023 that brought joy and laughter.

![Foods of 2023](/images/food-2023.jpg)
Savoring a variety of cuisines, as evidenced by the hundreds of food photos we took.

![Basketball 2023](/images/basketball-2023.jpg)
Enjoyed the thrill of playing basketball frequently this year.

![Bali Revisit 2023](/images/bali-vacation-2023.jpg)
Revisiting Bali after half a decade was a truly enriching experience.

![Bali Sunsets 2023](/images/bali-2023.jpg)
Enjoyed the stunning sunsets in Bali, ate tasty local food, read a lot, and had a great time catching up with Diego from WiFi Tribe.

## Highlights

Lots of good things happened. I enjoyed delicious food, traveled, and had fun with loved ones.

Here are the highlights of 2023:

- Joined [saas.group](https://saas.group/) as a Growth Manager.
- Played basketball for the local team “El Altet.” We secured the 8th position out of 16 teams by the end of the season.
- Co-worked with [Edgaras](https://edgaras.com/) on several workations.
- Visited places around Alicante, enjoyed looking at the sights, went on hikes, and chilled on the beaches.
- Returned to coding and learned how to use AI to assist me with Python and Next.js.
- Redesigned and rebuilt my blog using Next.js.
- Rebranded [Best Writing](https://bestwriting.com/) to [Craftled](https://craftled.com/), and decided to adopt a portfolio approach instead of focusing on building one thing.
- Met lots of smart folks at the saas.grouping event in Barcelona.
- Traveled around Lithuania and Latvia, introducing my wife’s mother to my home country and my family.
- Officially proposed to my wife. We got married in Lithuania in 2019, but I had never proposed. We want to remarry in Colombia soon.
- Attended a 3-day music festival, Mad Cool, in Madrid and danced to music by Robbie Williams, The Offspring, Sam Smith, Tash Sultana, Kaleo, Rüfüs Du Sol, Years & Years, and Red Hot Chili Peppers.
- Enjoyed an incredible 3-week vacation in Bali, with short stops in Sofia, Abu Dhabi, Dubai, and Singapore.
- Launched a [digital product on Gumroad](https://store.craftled.com/l/websites-that-pay-writers?layout=profile) and made my first sales.
- Nearly revived my personal newsletter. I realized it needs to be short and unstructured for me to maintain it consistently.
- Achieved the best physical shape of my life. On the last day of the year, hitting a 5x5 90KG squat and a 5x5 100KG deadlift. I used [the StrongLifts](https://stronglifts.com/) app.
- Simplified my personal and business expenses and operations by canceling most subscriptions and consolidating everything into one spreadsheet.
- Caught up with Diego, the co-founder of [WiFi Tribe](https://wifitribe.co/). We exercised, shared meals, and had meaningful conversations. It had been 5 years since our last meeting, so it was wonderful to catch up in Bali.
- Explored Bali and the nearby islands of Gili Air and Lombok.

## Challenges

This year was mentally and emotionally demanding.

Attempting to control uncontrollable aspects took a toll on my mental well-being.

While I don't perceive challenges as inherently negative, I believe it's crucial to reflect on what didn't go as planned and understand why. This understanding can help adjust the approach or realize when it's time to move on and try something different.

- I had to liquidate most of my overly optimistic and over-leveraged investments in tech stocks and crypto.
- I intended to write more, but didn't manage to.
- I meditated, but not as much as I would have liked.
- The fear of missing out (FOMO) with AI was real, just like with the whole crypto hype train. I realized that I'm not immune to trends and hype and can easily be distracted by the latest shiny object. I'm working on developing my own guiding principles to avoid these short-term distractions in the future.

## Achievements

When I look at the bigger picture, I feel positive.

I'm confident that I'm heading in the right direction and gaining clarity about what truly matters to me.

- Joining [saas.group](https://saas.group/) provided financial stability and emotional peace, although it wasn't as lucrative as my freelance ventures.
- I built and published a Chrome extension that displays writing quotes on a new tab.
- I delved deeper into programmatic SEO.
- I learned about web scraping.
- I became a member of the Small Bets community.
- I took Janis Ozolins's course on creating visuals and we became friends when he and his family moved to Alicante.
- I had engaging random calls and chats with fellow indie hackers, creators, marketers, and founders.
- I read several books, notably Die With Zero, and Arnold’s new book, Be Useful.
- I learned how to use Next.js and deploy websites to Vercel.
- I learned how to automate tasks using [Make.com](https://make.com/), Google Sheets, and code.
- I maintained a regular exercise routine (gym, basketball, padel, hiking).
- I practiced intermittent fasting.
- I took a complete break from work and vacationed in Lithuania and Bali.
- I visited six countries: Lithuania, Latvia, Bulgaria, United Arab Emirates, Indonesia, and Singapore.

## Life Lessons of 2023

Here's what I learned in this overwhelming year:

**Simplicity lasts.** Complexity is easy to break and hard to sustain. Always aim for simplicity.

**Leverage is key.** There's no direct correlation between the hours you spend and the effort and goodwill you put into work. Only leverage matters.

**Do more of what only you can do.** AI is intimidating because it may render me irrelevant. The only antidote is to be uniquely you and do things that only you can do.

**Be kind.** We all have bad days, but that's not a license to be rude. Be kind to yourself and others.

**Tinkering beats planning.** You can spend months or years stressed trying to create a master plan, or you can try something you feel might work and see for yourself. If it works, great, iterate to make it better. If it doesn't, move on and try something new.

**Ship fast, ship often.** In a world with infinite variables, you can't know what will work until you try. Your opinion doesn't matter.The market will tell if it's useful or not. Act on your energy burst. Storage doesn't work.

**Having an incredible wife is a life cheat code.** She's my peace, stability, and inspiration. I feel loved, heard, and challenged. It's us against the world.

**Listen and empathize.** Rather than trying to reason and respond, make the other person feel comfortable sharing and opening up, without judgment. It's enough to respond with "I hear you."

**You already know what to do.** For some reason, we seek validation, but we know what we must do right now. It's uncomfortable, it's hard, it's boring, but only complete focus towards one thing can bring you to it faster.

**If it's not on the calendar, it won't happen.** Use a calendar, reminders, notes, and other scheduling tools to commit yourself to something happening.

**Figure out the big stuff.** Decide the direction you want to go and don't sweat the small things, just do it. You can reflect later and see what works. You can't edit an empty page.

**You're a sum of your habits and decisions.** Make better decisions now if you want to be in a different place physically, emotionally, and financially.

**You can find contradicting advice for anything.** You will only know if you try.

**The only judge you have to impress is the future you.** Everyone plays a different game with different rules and prizes. Your game, your rules.

**Make your life tomorrow a bit easier.** Become 1% better every day, but also do somethingto your future self that will make your life 1% easier tomorrow.

**You don't have to have an opinion on everything.** You can just respond, "I don't know, I haven't actively thought about it."

**Do more stuff that can benefit from randomness.** Private notes, thoughts, and prototypes can't benefit from someone randomly discovering them. People buy even bad products, so put something out there and improve it later if it hurts.

**Close open loops.** Clean up your desktop, email inbox, messages, downloads folder, bookmarks, reminders, unsubscribe from newsletters, and cancel subscriptions. Most of these are just hanging there, taking your energy and headspace.

**State of mind matters.** You can't do your best work or think clearly when you're stressed.

**Creating in seasons is fine.** There are months when you learn, there are months where you explore, and there are months when you create more than in the last six months. That's fine.

**Follow what comes naturally right now.** The order usually doesn't matter, once you've set your goal and figured out the big stuff.

**Create luck.** Be in charge, call the shots, and make mistakes.

## Favorite Purchases of 2023

- Apple MacBook Air 15”
- Rockrider ST 120 mountain bike
- [Practical Programmatic](https://practicalprogrammatic.com/) course
- [Small Bets](https://smallbets.co/) membership
- [MakerKit](https://makerkit.dev/) SaaS boilerplate
- [Explain Ideas Visually](https://ozolinsjanis.com/course) course
- Golf shorts, pants, and T-shirts (I don’t play golf)
- [The Network Effects Masterclass](https://www.nfx.com/masterclass)
- [ChatGPT Prompt Engineering for Developers](https://www.deeplearning.ai/short-courses/chatgpt-prompt-engineering-for-developers/)
- Spotify
- HBO Max
- Apple iCloud
- Netflix

## Favorite Books of 2023

In the past, I used to measure my success by the number of books I managed to read in a year. I would even force myself to finish books that I didn't find enjoyable.

However, my approach has changed. Now, influenced by Naval, I choose books based on my current interests. If a book doesn't captivate me, I don't hesitate to skim through it or even leave it unfinished.

Fortunately, books have no feelings; otherwise, I would be in a lot of trouble.

Here are some of the books I enjoyed reading, listening to, or skimming through:

- Die with Zero by Bill Perkins
- A Short Stay in Hell by Steven L. Peck
- Be Useful by Arnold Schwarzenegger
- Greenlights by Matthew McConaughey
- The Almanack of Naval Ravikant by Eric Jorgenson
- The Untethered Soul by Michael A. Singer
- LeBron by Jeff Benedict
- Generating Product Ideas by Artiom Dashinsky

Thank you for taking the time to read this. Here's to the adventures that await in 2024!

If you’re interested in my previous annual reviews, check out my life lessons from [2013](/blog/life-lessons-2013), [2014](/blog/life-lessons-2014), [2015](/blog/life-lessons-2015), [2016](/blog/life-lessons-2016), [2017](/blog/life-lessons-2017), [2018](/blog/life-lessons-2018), [2019](/blog/life-lessons-2019), and [2020](/blog/life-lessons-2020).
