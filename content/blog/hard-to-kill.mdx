---
title: "Cockroach Startups: Build a Company That's Hard to Kill"
publishedAt: "2023-07-13"
updatedAt: "2024-08-26"
image: "/images/hard-to-kill.jpg"
location: "Alicante, Spain"
summary: "Like cockroaches, the most resilient startups adapt and thrive in any environment. Learn strategies from <PERSON> on building a company that's hard to kill."
---

Cockroaches are awesome, but only when we talk about startups and staying alive.

Cockroaches are resilient creatures capable of surviving extreme conditions.

Like cockroaches, startups need to be adaptable and robust to thrive in competitive environments.

[<PERSON>](https://www.paulgraham.com/index.html) leveraged a technological advantage early in his career by using Lisp, which enabled rapid iteration. He could implement new features daily, which is unheard of in corporate settings.

This raises an important question:

How can I make my tech stack and workflows so efficient that I can run more experiments?

**"The cheaper your company is to operate, the harder it is to kill." – <PERSON>**

Running lean allows more room for failure and flexibility.

If you minimize the costs, you immediately increase the upside and profitability.

Inspired by relentless resourcefulness, I decided to learn to code and moved my blog to [Vercel](https://vercel.com/), which is free for personal projects.

At [Best Writing](https://bestwriting.com), we minimized monthly costs to $300, and there's still room for optimization.

Source: [Founders Podcast, #275 <PERSON>'s Essays](https://founders.simplecast.com/episodes/275-paul-grahams-essays).
