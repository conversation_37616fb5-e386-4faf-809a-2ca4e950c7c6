---
title: "Life Lessons I Learned in 2024"
publishedAt: "2025-01-01"
location: "Chiang Mai, Thailand"
summary: "A personal reflection on life at 33: returning to Chiang Mai, building a business with family, traveling through Europe and Morocco, staying active, and sharing insights on productivity, wealth building, and favorite media of 2024."
---

8 years later, I'm back in Chiang Mai, Thailand, which feels familiar but also different. So many things have changed, and I'm probably not the same person I was when I first came here.

I just turned 33, and I'm now reflecting on the highlights, challenges, favorite purchases, and impactful books of 2024.

I realize how far I've come and how much I've learned since my last visit.

I'm happily married to the love of my life, <PERSON>, I'm in good health, I have an inspiring and challenging job, and I'm building a business with my best friend and my wife as my partners at [Craftled](https://craftled.com/).

Here's my annual review of 2024.

My typical process takes a few days to gather all the photos, notes, and calendar events and process them before publishing.

This year I found the [YearCompass app](https://yearcompass.vercel.app/), and I might be using it going forward.

Here's my year in photos:

![Roadtrip to Barcelona with Mom 2024](/images/roadtrip-to-barcelona-with-mom-2024.jpg)

Roadtrip to Barcelona with my mom.

![Malaga 2024](/images/malaga-2024.jpg)
Quick trip to Malaga to see Justas and Egle before they became parents.

![Marrakesh 2024](/images/marrakesh-2024.jpg)
Exploring Morocco with my wife Isabella.

![Marrakesh 2024](/images/morocco-2024.jpg)
Short trip to Marrakesh.

![Staying Active in 2024](/images/staying-active-in-2024.jpg)
Staying active in 2024, lifting weights, hiking, playing basketball, and padel.

![Working Out 2024](/images/working-out-2024.jpg)
Working out in 2024.

![Mantas and Debora Wedding Porto 2024](/images/mantas-and-debora-wedding-porto-2024.jpg)
My brother Mantas and his wife Debora's wedding in Porto.

![Mantas and Debora Wedding 2024](/images/mantas-and-debora-wedding-2024.jpg)
Having a great time at my brother Mantas and his wife Debora's wedding in Porto.

![Porto 2024](/images/porto-2024.jpg)
Exploring Porto, Portugal with my wife Isabella and my mom.

![Portugal 2024](/images/portugal-2024.jpg)
Sightseeing in Portugal.

![Catching Up with Edgaras in Denmark and Thailand 2024](/images/catching-up-with-edgaras-in-denmark-and-thailand-2024.jpg)
Catching up with Edgaras on his birthday in Denmark and visiting him in Thailand when he started his digital nomad journey with his girlfriend.

![London 2024](/images/london-2024.jpg)
Visiting London after many years.

![London Trip 2024](/images/london-trip-2024.jpg)
Meeting Rewadful team in London and attending Elevate 2024 affiliate marketing conference.

![Dad Visiting Me in Alicante 2024](/images/dad-visiting-me-in-alicante-2024.jpg)
My dad visiting me in my home in Alicante.

![Meeting Old Friends 2024](/images/meeting-old-friends-2024.jpg)
Meeting old friends.

![Meeting Friends and Eating Good Food 2024](/images/meeting-friends-and-eating-good-food-2024.jpg)
Meeting friends and eating good food.

![Bangkok 2024](/images/bangkok-2024.jpg)
Visiting Bangkok.

![Thailand 2024](/images/thailand-2024.jpg)
End of year Thailand vacation visiting Bangkok, Krabi, Ao Nang, Phuket, Chiang Mai, and Chiang Rai.

![Thailand Vacation 2024](/images/thailand-vacation-2024.jpg)
Sightseeing, chilling, drinking coffee, and eating good food in Thailand.

![NYE 2024](/images/nye-2024.jpg)
New Year's Eve in Chiang Mai, Thailand 2024.

## Highlights

Scrolling through my photos, calendar, and notes, I realize how lucky I am.

This year was close to ideal and there's little I would change.

It's been a great mix and balance of hard work, learning, spending time with my amazing wife, multiple family visits and seeing friends, and enjoying life.

Here are the highlights of 2024:

- Mom's visit and road trip to Barcelona, stopping at Peñiscola along the way. It was wonderful spending a week together in Alicante.
- Road trip to Malaga to catch up with my best friend Justas and his wife Egle just before they became parents.
- Spontaneous trip to Marrakesh.
- Flew to Copenhagen for my best friend and business partner's birthday - coding sessions with fruits, Aperol Spritz, and fast WiFi made for my kind of party.
- Great conversations with like-minded friends passionate about tech, health, and lifestyle design - caught up with Martynas, Justas, Edgaras, Sergio, Janis, Prean, Yosef, Ivan, Stefan, Orlando, Steven, Dominykas, Diana, Jeisson, Nisaide, Alice, Alisa, Louis, Chris, and Israel.
- My brother's wedding in Porto was a highlight - playing basketball, enjoying lots of food, and getting to know my brother's wife and her family in their hometown. We explored the city and indulged in too many pastel de nata.
- Joined [Rewardful](https://www.getrewardful.com/?via=tomaslau) as a Growth Manager, transitioning from saas.group to work with a small but mighty team building affiliate software for fast-growing AI and SaaS companies.
- My dad drove all the way from Lithuania to visit me in Alicante. It was heartwarming to see his childlike excitement while sightseeing and trying new foods.
- Returned to London after 8+ years to meet my Rewadful teammates in person and attend the inaugural Elevate 2024 affiliate marketing conference. Enjoyed walking around, spending time with Isabella's family, and catching up with my long-time friend and former business partner, Prean.
- 4-week Thailand vacation across Bangkok, Krabi, Ao Nang, Phuket, Chiang Mai, and Chiang Rai.
- Regularly collaborated with [Edgaras](https://edgaras.com/) on multiple projects.
- Redesigned and rebuilt (again) my [personal website](https://tomaslau.com/) using Next.js.
- Rebuilt [Best Writing](https://bestwriting.com/) using WordPress, learning the hard lesson that doing less but better is the answer.
- Nearly revived my personal newsletter, realizing it needs to be short and unstructured for consistency.
- Launched [Bordful](https://demo.bordful.com/), an open-source Next.js job board template.
- Created [Pynions](https://pynions.com/), an open-source Python framework for building AI-powered marketing workflows locally, after experiencing challenges with AI agent development.

## Challenges

I've lost two family members this year - my grandmother and my godfather. It's sad that I couldn't be there with my mom and family during those times.

This year I've been embracing my chaotic nature and surrendered to my multi-interested curiosity, building things without overthinking too much.

While I achieved and learned a lot this year, there were frustrating moments. Unlike prior years though, I now welcome challenges and frustration as old friends.

I know it's inevitable to struggle the first time, and I'm learning to enjoy the process.

Generally, I struggled with patience, keeping my mind focused on tasks at hand, and maintaining composure when dealing with people.

Delays, inefficiencies, indecisiveness, and other people's behavior can trigger frustration and anxiety about wasting time when I could be learning or building.

I realize it's my nature and I absolutely prefer spending quality one-on-one time with people, but I'm learning to enjoy group settings and being around more people.

While I'm embracing my generalist approach, I understand that while I'm great at starting things, multitasking isn't my strength.

I'm learning to focus on one thing at a time. Working on multiple projects simultaneously is doable but not optimal - I've struggled with completing things before starting something new.

As I age, I notice my recovery taking longer after intense workouts and basketball pickup games.

Despite eating healthy, taking supplements, and prioritizing sleep no matter where I am, I still got injured and found it challenging to stay positive while working on recovery and prevention.

I'm ending the year with a minor right shoulder injury that, while not serious, is annoying and limits my workout capacity.

I'm a workhorse and feel like I've figured out how to handle hard work, but I still struggle with completely disconnecting and relaxing to give my mind and body the breaks needed to recover and recharge for the work I love so much.

## Achievements

It's been a hectic year, as I moved fast and worked on multiple projects, but I'm proud of what I've achieved. I believe in figuring things out by shipping fast and iterating.

- Joined [Rewardful](https://www.getrewardful.com/?via=tomaslau) as a Growth Manager and worked on multiple growth initiatives, redesigning the homepage, launching targeted landing pages, optimizing content for SEO, and more.
- [Coded extensively](https://github.com/tomaslau) and learned how to use AI code editors like Cursor, Windsurf, Lovable, Bolt, and Replit.
- Shipped various tools and experiments.
- Launched [Marketful](https://marketful.com/) and [UI Things](https://uithings.com/).
- Learned about self-hosting and deploying websites on Hetzner.
- Started two open-source projects: [Pynions](https://pynions.com/) and [Bordful](https://demo.bordful.com/).
- Listened to loads of inspiring [Founders podcast](https://www.founderspodcast.com/) episodes while walking and washing dishes.
- Practiced intermittent fasting, skipping breakfast on most days.
- Took a month-long break from work and vacationed in Thailand with my wife, her cousin, family, and friends.
- [Visited five countries](https://nomads.com/tomaslau): Morocco, Denmark, Portugal, United Kingdom, and Thailand.
- Joined [Bluesky](https://bsky.app/profile/tomaslau.com) and rejoined [Twitter/X](https://x.com/tomaslaucom) (after deleting it in 2018) and started posting more regularly, inspired by Ross Simmonds' book Create Once, Distribute Forever.
- Maintained consistent exercise using [StrongLifts](https://stronglifts.com/) with 143 gym workouts in 2024, averaging 134,516 kg monthly volume. Despite a shoulder injury, achieved personal bests:
  - **Deadlift 5x5:** 120 kg (Feb 2024)
  - **Squat 5x5:** 97.5 kg (Sep 2024)
  - **Bench Press 5x5:** 90 kg (Sep 2024)
  - **Overhead Press 5x5:** 58 kg (Jul 2024)

## Life Lessons of 2024

Here's what I learned in this fast-paced year:

**Everyone at every stage compares themselves to others.** It's fine. Don't demonize yourself. Everything is fine. Just catch yourself doing it, say, "thanks, judge," and move on.

It's fine to slow down or take one step back to take two steps forward.

**Everyone wants to give you advice on how to live.** But they see only 0.01% of your life. Don't get discouraged or offended. Just thank them, apply the good, and discard the rest.

**You can and should break promises you made to yourself and others.** Stubborn people are not fun to be around.

**The best investment you can make is not crypto, not gold, not picking stocks, or investing in index funds.** Not even early-stage investing or trying to spot the next hot trend. It's you and your business. Not saying it's going to work out 100%, but it's your best chance to succeed given the mediocre circumstances and excluding luck. Yes, people get lucky with gold, trading, crypto, and picking stocks, but these are rare cases. You have a better chance betting on yourself since you know yourself and have some sort of control over inputs and outputs. Betting on yourself and thinking long-term is probably the best way to get what you want.

_"Happy is the man who can recognize in the work of Today a connected portion of the work of life, and an embodiment of the work of Eternity. The foundations of his confidence are unchangeable, for he has been made a partaker of Infinity."_ – James Clerk Maxwell

**The only way to guess the future right is to work on your projects and follow curiosity.** You simply can't predict the next Internet, Bitcoin, or ChatGPT moment. Get exceptionally good at things you care about, and your preparation will align with the right opportunity when everything falls into place and looks like you just won the lottery.

**Absolutely everyone has doubts.** Even if things are going well and especially if not.

_"**What if it's not worth it?** Another common reason for procrastination is that you are not convinced that a given action is worth the time and effort. Or, more subtly, you might believe it is worth the time and effort in general, but not for you."_ – Luca Dellanna

**Don't play Russian roulette.** Protecting yourself from the risk of ruin is more important than exposing yourself to a small chance of winning big.

**Looking out in space is like [looking back in time](https://webbtelescope.org/contents/articles/how-does-webb-see-back-in-time).**

> The light we see today from galaxies in the Virgo Cluster started on its path toward us at the same time as the age of the dinosaurs was ending on Earth. If you were in a Virgo Cluster galaxy today, and you had a telescope powerful enough to study the Earth, you would be able to see the prehistoric reptiles.

_"A writer is working when he's staring out of the window."_ – Burton Rascoe

_"Building 50% of a thing isn't 50% as productive, it's 0% as productive. There are few things less productive than work that is thrown away."_ – Jeremy Mikkola

_"I love Brent Beshore's observation that, 'I am 100% happy to watch you get really rich doing something that I have no interest in doing.' It's not always a competition."_ – Morgan Housel

Selling might be just eliminating objections until there's extreme clarity that you are getting good value for money.

**Don't worry about talking about what interests you.** Being yourself attracts like-minded people and repels the boring ones. It's easier to sell a hotdog to a hungry person than trying to convince someone to get more food after lunch.

**Once you find something you are good at, enjoy doing, and people can pay for it, stick to it.** You'll get unfairly good over the years, making competition irrelevant.

**Be boring with personal finance.** Cut costs, cancel subscriptions, save money, invest in VWCE every month, and act very conservatively. Your personal finances have one job only: to keep you in the game indefinitely. Don't get ruined with big risks. On the other hand, regularly take calculated risks with your business, and create a business entity if you don't have one yet.

**You don't want to retire and do nothing.** You want to find something you want to do indefinitely.

**It's fine to change your mind.** What does 18-year-old me know about 33-year-old me's interests and preferences?

**It's never too late to start playing long-term games.** Just by staying in the game long enough, you increase your chance of success since you expose yourself to more randomness that can benefit you.

_"The special ops guys and the firefighters around the world have this great phrase. They say, 'Slow is smooth, and smooth is fast,' and that is true. Everything I've accomplished in my life has been because of that attitude."_ — Jeff Bezos

**If you're building a startup today, your real moats are in the Three D's: Data, Distribution, and Design.**

- **Data:** How well you understand your users and their behavior.
- **Distribution:** Your unique way of reaching and connecting with customers.
- **Design:** Your taste and craftsmanship.

Shane Levine, Founder at Turbo

Questions helping decision-making:

- Will it give me more options?
- Will it save time?
- Will it make me more money?
- Will it make me happier?
- Will it make me healthier?
- Will I have to maintain it?
- Can I combine it with other things? e.g. hike = socialize + exercise

**Building stuff >** books > courses > personal blogs > podcasts > documentaries > social media.

**Live frugally.** Money in the bank can give you better peace of mind and options to get away from most bad situations, focus on unprofitable but fulfilling projects, and invest in your health by buying faster private health services. I just canceled a bunch of personal subscriptions. I am still at $1,300/year (most expensive being the gym, mobile plan, and Apple iCloud). That's like $40,000 over the next 30 years.

_"**Never risk your long-term assets.** No bet of yours should, in case of failure, destroy your health, capital, relationships, and trustworthiness."_ – Luca Dellanna

_"How many speeches must you make to become a good public speaker? How many products do you need to launch to become a competent entrepreneur? **How many failures do you need to succeed?** Ensure you have a strategy that can afford that number of attempts. Hopefully, you will succeed faster, maybe even on your first try. But if you want your success to be inevitable, you must be able to afford to make many attempts - and start making them as soon as possible."_ – Luca Dellanna

_"You waste years by not being able to waste hours."_ – Amos Tversky

_"Ninety percent of success can be boiled down to **consistently doing the obvious thing for an uncommonly long period of time** without convincing yourself that you're smarter than you are."_ – Shane Parrish

_"General ambition will give you anxiety. Specific ambition will give you direction."_ – Anu

**Trajectory over goals.** It's fun to set goals, but they can be misleading while you achieve your goals focusing on the wrong thing, or they can be devastating as you focus on outcomes rather than inputs. Reviewing your life trajectory, health, relationships, and work is a better indicator as there are inevitably good and bad days, but your average should always be an upward trend.

**The internet is for everyone, but use cases are individual.** Don't treat every channel, platform, tool, or opportunity equally.

**Social media is for discovering things and learning opinions.** Search is for finding things you already know exist. Keep this in mind when marketing your business and tailor your content for each channel.

**Do things you say you'll do.** A simple but not easy way to build trust, authority, and reputation.

**Take care of your body hardware and software.** Your body is your hardware and software; constant maintenance, good sleep, and exercise are needed if you want to stay long in the game. There are days when going to the gym feels like work, but it is work—maintenance work and installing updates—boring, tedious, but needed.

_"Short-term players almost see winning long-term games as a failure. 'You could have achieved that faster.' So, short-term players take actions that are unsustainable over the long term. They sacrifice their health or personal lives, incur excessive spending, consume their trust capital, take small risks that could go wrong in serious ways, and so on. Conversely, long-term players only use strategies that they can sustain over the long term."_ – Luca Dellanna

**Play on easy mode.** Hustle culture looks good on social media, but you get better odds if you work on easy mode. Easier problems, easier solutions, easier work schedule. This way, you can sustain it long-term and play the game indefinitely.

Boring businesses give freedom and resources to work on challenging and interesting projects.

**Distribution is becoming more important than creation.** Finding a way to reach users and give them what they want is harder than ever.

You don't have to make money from the same place you lost it.

_"Uber was just one of many projects Kalanick juggled in 2010. Zuckerberg started quite a few software projects before Facebook. Sure, most of those were tiny, but was it so obvious before the fact that Facebook would become so big? Probably, for at least some time, it looked like 'just another project.' And J.K. Rowling wrote another novel and more minor projects before her world-famous Harry Potter. Most successful people start with a portfolio of bets that later converge to a single, major, winning bet. The keyword is 'later.'"_ – Luca Dellanna

## Favorite Purchases of 2024

I don't really buy much stuff these days. Mostly digital subscriptions to improve my productivity, but this year I've been obsessed with coding and absolutely loved Cursor, the AI code editor.

I'm also very happy with Windsurf as it understands my coding style better with certain projects.

I'm in Thailand now, traveling for a month and got a battery case. I usually don't use a case for my iPhone as I don't like how it feels, but exploring and using maps + data drains my battery quickly.

I got this [iPhone charger](https://www.amazon.es/gp/product/B00QUU6T0E/ref=ox_sc_act_title_12?smid=A217EVZJH2JMV5&psc=1) and it works great!

- [ZeroWork](https://www.zerowork.io/)
- [Cursor](https://www.cursor.com/)
- [Claude AI](https://claude.ai/)
- [Typefully](https://typefully.com/)
- [Windsurf](https://codeium.com/windsurf)
- [Screaming Frog](https://www.screamingfrog.co.uk/seo-spider/)
- [Achromatic](https://www.achromatic.dev/)

## Favorite Books of 2024

I [proudly claim](https://tomaslau.com/about) that I love books and have an Everand subscription until 2351. However, this year I didn't read as much as I would have liked.

I found myself spending more time on Twitter (now X) than I should. While scrolling feels good and addictive just like potato chips, I barely retain anything. I can't even remember what I saw on social media an hour ago, let alone a month ago.

Books, though sometimes feeling boring and slow, requiring that magical activation energy to start reading, are still superior to digital content. The repetitive stories and lengthy examples help cement the main ideas in my brain.

I primarily listened to the Founders podcast this year, and noticed that most people I admire are voracious readers.

While not the most practical way to store and consume books, physical books inspire me and give me a different sense of accomplishment.

I've realized that like some people enjoy shopping malls, I love browsing bookstores and libraries - not necessarily to buy books, but to feel humbled by the abundance of knowledge and ideas.

It reminds me of my insignificant existence and the fact that I'll never read all the books in the world.

My approach to reading has evolved.

Instead of measuring success by the number of books completed or forcing myself to finish unenjoyable reads, I now follow Naval's approach of choosing books based on current interests.

If a book doesn't captivate me, I don't hesitate to skim or leave it unfinished.

Fortunately, books have no feelings (not yet).

Here are some of the [books I enjoyed reading](https://www.goodreads.com/author/show/14358907.Tomas_Laurinavicius), listening to, or skimming through:

- Fooled by Randomness by Nassim Nicholas Taleb
- The Untethered Soul by Michael A. Singer
- Never Enough by Andrew Wilkinson
- Only the Paranoid Survive by Andrew S. Grove
- Winning Long-Term Games by Luca Dellanna
- The Mom Test by Rob Fitzpatrick
- Hackers & Painters by Paul Graham
- Optionality by Richard Meadows
- Crypto Confidential by Nat Eliason
- Alchemy by Rory Sutherland
- Steal Like an Artist by Austin Kleon
- Create Once, Distribute Forever by Ross Simmonds

## Best Movies & TV Shows I Watched in 2024

- Society of the Snow (2023)
- Poor Things (2023)
- Griselda (2024)
- The Creator (2023)
- Dune: Part Two (2024)
- All the Light We Cannot See (2023)
- Kaos (2024)
- From (2022)
- Dark Matter (2024)
- Gladiator II (2024)
- Silo (2023)
- Beef (2023)
- Severance (2022)
- Band of Brothers (2001)
- One Hundred Years of Solitude (2024)

## Looking Forward to 2025

Here are my predictions and goals for 2025:

- Squat 100kg
- Achieve pain-free shoulders through consistent band exercises
- Bitcoin will dip to the low $40,000s during 2025
- It's unlikely I'll revive my personal newsletter
- Explore self-publishing and release 5 books
- Continue avoiding TikTok
- Consider starting a YouTube channel
- Resist the urge to start a podcast
- Practice intermittent fasting with a 9:00-16:00 eating window (skipping dinner)
- Witness and participate in the renaissance of personal blogging
- Read more physical books
- Write more short-form and long-form essays
- Focus on consuming existing courses and books rather than buying new ones

Thank you for taking the time to read this. Here's to a prosperous 2025!

If you’re interested in my previous annual reviews, check out my life lessons from [2013](/blog/life-lessons-2013), [2014](/blog/life-lessons-2014), [2015](/blog/life-lessons-2015), [2016](/blog/life-lessons-2016), [2017](/blog/life-lessons-2017), [2018](/blog/life-lessons-2018), [2019](/blog/life-lessons-2019), [2020](/blog/life-lessons-2020), and [2023](/blog/life-lessons-2023).
