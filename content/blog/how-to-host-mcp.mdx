---
title: "4 Quick Ways to Host an MCP Server in 2025"
publishedAt: "2025-06-10"
location: "Alicante, Spain"
summary: "LLM agents need an MCP endpoint, not a Swagger file. Below are four fast ways to wrap your API in MCP and put it online."
---

LLM agents need an MCP endpoint, not a Swagger file. Below are four fast ways to wrap your API in MCP and put it online.

## Quick Comparison

| #   | Platform                 | Time to live                   | Starting price                                              | Good when…                                    |
| --- | ------------------------ | ------------------------------ | ----------------------------------------------------------- | --------------------------------------------- |
| 1   | **Cloudflare Workers**   | Under 2 min (one click deploy) | Free up to 100 k requests/day. Paid plan $5/mo.             | You want a low-cost global edge.              |
| 2   | **Make.com**             | 3–5 min, no code               | Free 1 k ops/mo. Core $9/mo.                                | Teams that already build flows in Make.       |
| 3   | **Azure API Management** | 10–15 min                      | Basic v2 $150/mo, Standard v2 $700/mo, Premium v2 $2 801/mo | You use APIM and need VNet or policy support. |
| 4   | **Kong Konnect**         | 15–20 min                      | OSS gateway free. Konnect SaaS: ask sales.                  | You run Kong and want MCP as a plug-in.       |

Pricing sourced in June 2025.

---

## 1. Cloudflare Workers

- **Deploy:** Click **“Deploy to Workers.”** The template spins up a remote MCP server.
- **Free quota:** 100 k requests each day.
- **Paid plan:** $5 a month plus usage.
- **Why pick it:** Runs at the edge. Cold starts are rare.

[Cloudflare guide](https://developers.cloudflare.com/agents/guides/remote-mcp-server/) / [Pricing](https://developers.cloudflare.com/workers/platform/pricing/)

---

## 2. Make.com

- **Deploy:** Open any Scenario, switch it to **On Demand**, and Make hosts the MCP server for you.
- **Free quota:** 1 000 operations a month.
- **Paid plans:** Start at $9 a month for 10 000 ops.
- **Why pick it:** Works without code. Good for non-developers.

[Make developer hub](https://developers.make.com/mcp-server) / [Pricing](https://www.make.com/en/pricing)

---

## 3. Azure API Management

- **Deploy:** In the portal choose **APIs → MCP Servers → Create.**
- **Pricing:** MCP is only on v2 SKUs. Basic $150, Standard $700, Premium $2 801 per month.
- **Why pick it:** Keeps all APIM features—policies, VNet, analytics.

[How-to](https://learn.microsoft.com/azure/api-management/export-rest-mcp-server) • [pricing](https://azure.microsoft.com/pricing/details/api-management/)

---

## 4. Kong Konnect

- **Deploy:** Enable the MCP plug-in in Konnect or in a self-hosted Gateway 3.10+.
- **Pricing:** Open-source gateway is free. SaaS plans are quote-based.
- **Why pick it:** Fits teams that already route traffic through Kong.

[Launch post](https://konghq.com/blog/product-releases/mcp-server)

---

## Which One Should You Use?

| Need                                | Pick                   |
| ----------------------------------- | ---------------------- |
| The fastest public endpoint         | **Cloudflare Workers** |
| A no-code option                    | **Make.com**           |
| Enterprise network controls         | **Azure APIM**         |
| A plug-in for an existing Kong mesh | **Kong Konnect**       |

**Bottom line:** You can turn an API into an MCP server in minutes. Choose the tool that matches your stack and budget.
