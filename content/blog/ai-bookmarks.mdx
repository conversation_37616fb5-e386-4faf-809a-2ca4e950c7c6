---
title: "AI Bookmarks (Reading List and Tools)"
publishedAt: "2023-07-16"
updatedAt: "2024-05-12"
location: "Alicante, Spain"
summary: "My fast-changing list of AI bookmarks, including reading list, case studies, tools, courses, and trusted people."
---

Here's my fast-changing list of AI bookmarks, including reading list, case studies, tools, courses, and trusted people.

## Large Language Models (LLMs)

- [AI21 Studio](https://www.ai21.com/studio)
- [Cohere](https://cohere.com/)
- [Gemma](https://ai.google.dev/gemma/) (Open Source) [Gemma on Hugging Face](https://huggingface.co/google/gemma-7b-it)
- [Mixtral-8x7B](https://huggingface.co/mistralai/Mixtral-8x7B-v0.1)
- [Ollama](https://ollama.com/): Open-source tool for building and running language models on a local machine.

## Case Studies

- [Extract Structured Data From Text: Expert Mode (Using Kor)](<https://github.com/gkamradt/langchain-tutorials/blob/main/data_generation/Expert%20Structured%20Output%20(Using%20Kor).ipynb>)
- [Free Script](https://colab.research.google.com/drive/1NeJbtLGCEszwAkaq9KQ_9Ai-52wmAska#scrollTo=rL-UYrcMGNY_) - Automatic Schema Enhancement: Improve Search Visibility With the Help of GPT4 via [Kristin Tynski](https://www.linkedin.com/posts/kristintynski_gpt4-googlecolab-python-activity-7058863537414131713-ggIV?utm_source=share&utm_medium=member_desktop)
- [Use ChatGPT to clean company names](https://burly-dessert-828.notion.site/Use-ChatGPT-to-clean-company-names-d714eabddc9442fbb969e1924fd1ea1f)
- [Using GPT-3 To Automate Your Workflows (Full Tutorial)](https://www.youtube.com/watch?v=ZrpKQVNtTws)

## Prompts

- [ChatGPT can access the internet, use APIs and display images! Might need a few tries and often throws a final "network error" but it works.](https://twitter.com/SchmartinMartin/status/1602462324429922310)

## Blogs

## Lists

- [Supertools](https://supertools.therundown.ai/)

## People

- [Greg Kamradt](https://github.com/gkamradt), [@GregKamradt](https://twitter.com/GregKamradt)

## Tools

| Tool                                                                         | Description                                                                                                                                                                                                                                                                                                              | Open-Source |
| ---------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ----------- |
| [Phind](https://www.phind.com/)                                              | Personal search-enabled assistant for programmers.                                                                                                                                                                                                                                                                       | No          |
| [LangChain](https://python.langchain.com/docs/get_started/introduction.html) | A framework for developing applications powered by language models. It enables applications that are data-aware and agentic.                                                                                                                                                                                             | Yes         |
| [LlamaIndex](https://gpt-index.readthedocs.io/en/latest/index.html)          | LlamaIndex (GPT Index) is a data framework for your LLM application.                                                                                                                                                                                                                                                     | Yes         |
| [Chroma](https://www.trychroma.com/)                                         | The AI-native open-source embedding database.                                                                                                                                                                                                                                                                            | Yes         |
| [Vectara](https://vectara.com/)                                              | Vectara is a developer-first API platform for easily building conversational search experiences that feature best-in-class Retrieval, Summarization, and “Grounded Generation” that all but eliminates hallucinations.                                                                                                   | No          |
| [Chainlit](https://chainlit.io/)                                             | An open-source asynchronous Python framework for building scalable conversational AI or agentic applications quickly and efficiently.                                                                                                                                                                                    | Yes         |
| [Embedchain](https://embedchain.ai/)                                         | An open-source retrieval-augmented generation (RAG) framework that simplifies the creation and deployment of AI applications using RAG models.                                                                                                                                                                           | Yes         |
| [CrewAI](https://www.crewai.io/)                                             | An open-source Python framework designed to orchestrate the collaboration of autonomous AI agents, enabling them to work together seamlessly to accomplish complex tasks.                                                                                                                                                | Yes         |
| [Devv.ai](https://devv.ai/)                                                  | A next-generation search engine for developers offering various modes like Fast Mode for quick answers, Agent Mode powered by GPT-4 for complex questions, and GitHub Mode for interacting with repositories. Provides lightning-fast responses, documentation, and code snippets to assist developers in their queries. | No          |

## Courses

- [ChatGPT Prompt Engineering for Developers](https://www.deeplearning.ai/short-courses/chatgpt-prompt-engineering-for-developers/)
- [Learn Prompting](https://learnprompting.org/)
- [LLM University](https://docs.cohere.com/docs/llmu)
- [LangChain: Chat with Your Data](https://www.deeplearning.ai/short-courses/langchain-chat-with-your-data/)

To be continued...
