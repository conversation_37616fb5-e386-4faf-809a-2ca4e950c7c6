---
title: "Contentlayer Issue Fix When Upgrading to Next.js 14.2+"
publishedAt: "2024-06-07"
image: "/images/contentlayer-nextjs-upgrade-issue-fix.png"
location: "Alicante, Spain"
summary: "How to resolve Contentlayer issues when upgrading to Next.js 14.2+ from version 13 with a simple fix in your package.json file."
---

![Contentlayer issue fix when upgrading to Next.js 14.2+](/images/next-contentlayer-overrides-fix.png)

If you're encountering an issue with [Contentlayer](https://contentlayer.dev/) when trying to upgrade to Next.js 14.2 or later from version 13, there's a simple fix that can resolve the problem.

The issue arises due to compatibility problems between `next-contentlayer` and Next.js 14.2+. Next.js 14.2 introduced some breaking changes that caused issues with the Contentlayer package.

Specifically, the `next-contentlayer` plugin, which integrates Contentlayer with Next.js projects, had a peer dependency on `next` versions `^12 || ^13`.

However, with Next.js 14, the `next` package was updated to a newer version, causing a peer dependency mismatch when trying to install `next-contentlayer`.

This resulted in errors like `npm ERR! ERESOLVE unable to resolve dependency tree` when attempting to install `next-contentlayer` in a Next.js 14 project.

The root cause is that the `next-contentlayer` package was not immediately updated to support the latest `next` version 14, leading to the peer dependency conflict when upgrading to Next.js 14.2+.

In your project's `package.json` file, add the following code under the `"devDependencies"` section:

```json
"overrides": {
  "next-contentlayer": {
    "next": "$next"
  }
}
```

This override tells Next.js to use the latest version of the `next` package for the `next-contentlayer` dependency, resolving the compatibility issue.

After adding this code, save the `package.json` file, and you should be able to upgrade to Next.js 14.2+ without any Contentlayer-related issues.

That's it! With this small change, you can smoothly upgrade your Next.js project to the latest version while ensuring that Contentlayer continues to work as expected.

Thank you [Edgaras](https://edgaras.com/) for the tip!
