---
title: "SOPs and The Boring Art of Documentation"
publishedAt: "2023-07-17"
image: "/images/sops.jpg"
location: "Alicante, Spain"
summary: "Discover the practical side of documenting things through standard operating procedures (SOPs) and learn about helpful tools such as Google Docs, Notion, Loom, Descript, Tango, and Scribe."
---

Documenting things is often considered boring, but there's a bright side to it.

Standard operating procedures (SOPs) can significantly increase productivity and reduce wasted time and resources.

Whether you're a solo freelancer, agency, or a large company, implementing SOPs can bring numerous benefits. Even if you're not an expert at creating documentation, you can still experience the advantages.

To emphasize the significance of documentation, I rebranded my newsletter from Life Designed to [Growthlog](https://tomaslau.com/newsletter), aligning it with the concept of documenting and logging personal and professional experiences.

With the rise of personal AI assistants, extracting and documenting processes has become easier than ever, leading to faster workflows and noticeable improvements.

To dive deeper into the why, how, and what of SOPs, I recommend watching the insightful presentation by web designer <PERSON>: [How to scale a web design agency with standard operating procedures](https://www.youtube.com/watch?v=hG3vA6QBqfA).

[![How to scale a web design agency with standard operating procedures](/images/mcdonalds-sops-example.png)](https://www.youtube.com/watch?v=hG3vA6QBqfA)

Implementing SOPs can transform your workflow from one characterized by lack of clarity, wasted time, and errors to a more efficient, quality-driven process with predictable turnaround times.

Checklists provided by SOPs help ensure quality and reduce the chances of mistakes.

| Before SOPs                                                                                        | After SOPs                                                                              |
| -------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------- |
| Lack of clarity and direction, wasted time, mentally exhausting, quality suffers, prone to errors. | Predictable turnaround times, ensures quality, efficient, checklists help avoid errors. |

Here are some recommended tools for creating and implementing SOPs:

- [Google Docs](https://docs.google.com/): Ideal for collaborative work on SOPs.
- [Notion](https://www.notion.so/): A versatile tool for creating knowledge bases and SOPs, offering [SOP templates](https://www.notion.so/templates/category/standard-operating-procedure-sop).
- [Loom](https://www.loom.com/): Enables quick video recordings that guide others through your processes step-by-step.
- [Descript](https://www.descript.com/): Provides automated text transcriptions (additional payment may be required) and advanced editing tools.
- [Tango](https://www.tango.us/) and [Scribe](https://scribehow.com/): Advanced tools for taking documentation to the next level.

In the past, when I used to design, I wrote numerous tutorials explaining how to use Photoshop, involving the manual capture of many screenshots.

With tools like Scribe, creating tutorials has become significantly easier. Explore the [Scribe gallery](https://scribehow.com/gallery) to discover a wealth of walkthroughs created by people like you.

Overall, implementing SOPs and utilizing the recommended tools can revolutionize your workflow, bringing clarity, efficiency, and improved quality to your processes.
