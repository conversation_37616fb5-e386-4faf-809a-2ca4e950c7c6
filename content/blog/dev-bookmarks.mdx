---
title: "Dev Bookmarks (GitHub Repos, Reading List and Tools)"
publishedAt: "2024-02-24"
location: "Alicante, Spain"
summary: "My fast-changing list of dev bookmarks, including GitHub repos, reading list, tools, courses, fun experiments, and makers."
---

Here's my fast-changing list of dev bookmarks, including GitHub repos, reading list, tools, courses, fun experiments, and makers.

## Large Language Models (LLMs)

## Search

| Tool                                               | Description                                                                                                      | Price |
| -------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------- | ----- |
| [Grep.app](https://grep.app/)                      | A search tool that allows users to search across over half a million Git repositories using regular expressions. | Free  |
| [Globe Explorer](https://explorer.globe.engineer/) | Super fast and cool topic researcher.                                                                            | Free  |

## Prompts

## Blogs

## Lists

## People

## Tools

## CMS

| Tool                               | Description                                                                      | Price | Open-Source |
| ---------------------------------- | -------------------------------------------------------------------------------- | ----- | ----------- |
| [Pages CMS](https://pagescms.org/) | A user-friendly CMS for static site generators (Next.js, Astro, Hugo, and Nuxt). | Free  | ✅          |

## Repositories

## Courses

To be continued...
