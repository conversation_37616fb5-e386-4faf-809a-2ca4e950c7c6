---
title: "Growthlog 5: Life is a game"
publishedAt: "2023-08-30"
image: "/images/doubt.jpg"
location: "Alicante, Spain"
summary: "Next week, I’ll be traveling to Barcelona to meet the remote team of hundreds of people for the first time."
---

Hola! It's Tom from 🌵 [Growthlog](https://tomaslau.com/).

Earlier this year I joined [saas.group](http://saas.group) as a Growth Manager.

Next week, I’ll be traveling to Barcelona to meet the remote team of hundreds of people for the first time.

I’m excited and anxious at the same time as I don’t know if I can still behave as a decent human after working for so long from home.

Here are some interesting things I discovered and reread this week.

---

🕹️ **Life Is a Game.** One of my all-time favorite blog posts by <PERSON>. This is your [strategy guide](https://oliveremberton.com/2014/life-is-a-game-this-is-your-strategy-guide/) to winning at life.

![Drink vs. code](/images/newsletter/drink-vs-code.png)

_“Your willpower level is especially important. Willpower fades throughout the day, and is replenished slightly by eating, and completely by a good night’s sleep. When your willpower is low, you are only able to do things you really want to.”_

_”It’s almost impossible to get rich working for someone else. Riches do not come from work alone, they come from owning things – assets – that pay back more than they cost, and your own company is a powerful asset you can create from scratch. Compound your winnings into more assets, and eventually they can remove your need to work at all.”_

🐍 **Python Scripts.** 20+ [free Python scripts](https://www.linkedin.com/feed/update/urn:li:activity:7102342397937737729/) for automating SEO, content marketing, PR, and social media with AI.

These Python scripts can replace tons of expensive tools and get better SEO and marketing results.

At the beginning of this year, I only heard about Python and had a basic understanding of programming.

Now, after months of playing with code and getting instant feedback from ChatGPT, I'm comfortable enough to do more technical marketing, and the results can be 10x or even 100x (for example, I learned how to generate 1000s of blog posts from a single dataset in Google Sheets).

While it may look complicated and hard, I encourage you to try it out.

[Kristin Tynski](https://www.linkedin.com/in/kristintynski/) has been [great inspiration](https://tomaslau.com/blog/seo-bookmarks) to me in showing what's possible and generously sharing learnings and actual code that I can copy and run pretty much instantly.

🗣️ **Speechy.** AI-driven [speaking assistant](https://speechy.ai/), offering personalized feedback to enhance your speaking skills. Handy for people learning English. I haven’t used it yet.

👩‍💻 **Public APIs.** If you’re building stuff online you can benefit from this collective [list of free APIs](https://github.com/public-apis/public-apis).

🚲 **Zone 2 Training.** My friend Alisa Saleh, MD wrote an excellent piece explaining what [zone 2 training](https://healthspanjourney.substack.com/p/take-it-more-slow-to-live-longer) is and why it’s important for longevity.

_“Since I do my Z2 exercise on a stationary cycling, I can use my iPad to watch movies or series.”_

---

**My Projects**

✍️ **Best Writing**. Connecting writers with [job opportunities](https://bestwriting.com/).

- Learning about search and considering [Typesense](https://typesense.org/) for writing job search engine prototype.

✈️ **Port Surfer**. Organizing world [airport information](https://portsurfer.com/). My programmatic SEO playground.

---

The best part of sending this newsletter?

Meeting creators and fellow founders.

Respond and introduce yourself or tell me more about your projects. I read every email.

Now is the time to 🎰 bet on yourself, to 🔨 build, to 🚢 ship, and to 🌱 grow.

_P.S. [Don’t self-reject](https://www.linkedin.com/feed/update/urn:li:activity:7099488179166081024/)._

---

Sent from my desk in Plaza Santa Teresa, Alicante, Spain. ☀️
