// Regex for splitting words, defined at top level for performance
const WORD_SPLIT_REGEX = /\s+/;

export function calculateReadingTime(content: string): string {
  const wordsPerMinute = 200; // Average reading speed
  const wordCount = content.trim().split(WORD_SPLIT_REGEX).length;
  const readingTime = Math.ceil(wordCount / wordsPerMinute);

  return readingTime === 1 ? '1 min read' : `${readingTime} min read`;
}
