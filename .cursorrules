When making decisions, please consider the following:

- The code has to be modular and easy to customize, reuse components as much as possible
- Avoid code bloat, keep the codebase clean and easy to understand
- When creating a new component, create it "in the style" of another element and embed that code, do not start from scratch to keep the codebase consistent
- Create a new component only when it is absolutely necessary

Do not overcomplicate things. Keep it simple.

Technical notes

- We use Next.js 14 with the App Router (app) and React Server Components.

Fundamental concepts

Our Next.js templates are designed to fully adhere to the new routing model provided by the App Router in Next.js. As a result, there will no longer be a pages/ directory. Instead, all pages will be contained within the new app/ directory.

Component Naming and Organization:

- kebab-case for file names (e.g., press-logos.tsx)
- PascalCase for component names (e.g., PressLogos.tsx)
- camelCase for functions/attribute names (e.g., pressLogos.tsx)
- UPPER_CASE for constants (e.g., const MAX_ITEMS = 10;)
