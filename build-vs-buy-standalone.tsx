'use client';

import { type ChangeEvent, useState } from 'react';

interface FormData {
  teamSize: string;
  hourlyRate: string;
  buildTime: string;
  maintenanceHours: string;
  saasPrice: string;
  expectedUsers: string;
}

interface Results {
  initialBuildCost: number;
  yearlyMaintenanceCost: number;
  yearlyInfrastructureCost: number;
  yearOneBuildCost: number;
  yearlyOngoingBuildCost: number;
  yearlySubscriptionCost: number;
  threeYearBuildCost: number;
  threeYearSaasCost: number;
  monthlyPerUserBuildCost: number;
  monthlyPerUserSaasCost: number;
  breakEvenMonths: number;
  recommendation: string;
  recommendationConfidence: number;
  costDifference: number;
  isBuildCheaper: boolean;
}

export default function BuildVsBuyCalculator() {
  const [formData, setFormData] = useState<FormData>({
    teamSize: '',
    hourlyRate: '',
    buildTime: '',
    maintenanceHours: '',
    saasPrice: '',
    expectedUsers: '',
  });

  const [results, setResults] = useState<Results | null>(null);

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const calculateCosts = () => {
    const teamSize = Number.parseInt(formData.teamSize);
    const hourlyRate = Number.parseFloat(formData.hourlyRate);
    const buildTime = Number.parseFloat(formData.buildTime);
    const maintenanceHours = Number.parseFloat(formData.maintenanceHours);
    const saasPrice = Number.parseFloat(formData.saasPrice);
    const expectedUsers = Number.parseInt(formData.expectedUsers);

    if (!teamSize || !hourlyRate || !buildTime || !maintenanceHours || !saasPrice || !expectedUsers) {
      alert('Please fill in all fields');
      return;
    }

    // Build costs
    const initialBuildCost = teamSize * hourlyRate * buildTime * 40; // 40 hours per week
    const yearlyMaintenanceCost = teamSize * hourlyRate * maintenanceHours * 52; // 52 weeks
    const yearlyInfrastructureCost = expectedUsers * 2; // $2 per user per year estimate

    const yearOneBuildCost = initialBuildCost + yearlyMaintenanceCost + yearlyInfrastructureCost;
    const yearlyOngoingBuildCost = yearlyMaintenanceCost + yearlyInfrastructureCost;
    const threeYearBuildCost = yearOneBuildCost + yearlyOngoingBuildCost * 2;

    // SaaS costs
    const yearlySubscriptionCost = saasPrice * expectedUsers * 12;
    const threeYearSaasCost = yearlySubscriptionCost * 3;

    // Per-user costs
    const monthlyPerUserBuildCost = yearlyOngoingBuildCost / 12 / expectedUsers;
    const monthlyPerUserSaasCost = saasPrice;

    // Break-even calculation
    const breakEvenMonths = initialBuildCost / (yearlySubscriptionCost / 12 - yearlyOngoingBuildCost / 12);

    const costDifference = Math.abs(threeYearBuildCost - threeYearSaasCost);
    const isBuildCheaper = threeYearBuildCost < threeYearSaasCost;

    setResults({
      initialBuildCost,
      yearlyMaintenanceCost,
      yearlyInfrastructureCost,
      yearOneBuildCost,
      yearlyOngoingBuildCost,
      yearlySubscriptionCost,
      threeYearBuildCost,
      threeYearSaasCost,
      monthlyPerUserBuildCost,
      monthlyPerUserSaasCost,
      breakEvenMonths,
      recommendation: threeYearBuildCost < threeYearSaasCost ? 'BUILD' : 'BUY',
      recommendationConfidence:
        (Math.abs(threeYearBuildCost - threeYearSaasCost) /
          Math.max(threeYearBuildCost, threeYearSaasCost)) *
        100,
      costDifference,
      isBuildCheaper,
    });
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold">Build vs Buy Calculator</h1>
        <p className="text-gray-600">
          Calculate whether it's more cost-effective to build a solution in-house or buy a SaaS product
        </p>
      </div>

      {/* Input Form */}
      <div className="bg-white border border-gray-200 rounded-lg p-6 space-y-6">
        <h2 className="text-xl font-semibold">Project Details</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="block text-sm font-medium">Team Size</label>
            <input
              type="number"
              name="teamSize"
              value={formData.teamSize}
              onChange={handleInputChange}
              placeholder="e.g., 3"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium">Hourly Rate ($)</label>
            <input
              type="number"
              name="hourlyRate"
              value={formData.hourlyRate}
              onChange={handleInputChange}
              placeholder="e.g., 100"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium">Build Time (weeks)</label>
            <input
              type="number"
              name="buildTime"
              value={formData.buildTime}
              onChange={handleInputChange}
              placeholder="e.g., 12"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium">Maintenance (hours/week)</label>
            <input
              type="number"
              name="maintenanceHours"
              value={formData.maintenanceHours}
              onChange={handleInputChange}
              placeholder="e.g., 10"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium">SaaS Price ($/user/month)</label>
            <input
              type="number"
              name="saasPrice"
              value={formData.saasPrice}
              onChange={handleInputChange}
              placeholder="e.g., 25"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium">Expected Users</label>
            <input
              type="number"
              name="expectedUsers"
              value={formData.expectedUsers}
              onChange={handleInputChange}
              placeholder="e.g., 50"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        <button
          onClick={calculateCosts}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
        >
          Calculate Costs
        </button>
      </div>

      {/* Results */}
      {results && (
        <div className="space-y-6">
          {/* Recommendation */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Recommendation</h2>
            <div className="text-center space-y-2">
              <div className="text-2xl font-bold">
                {results.isBuildCheaper ? (
                  <span className="text-green-600">BUILD IN-HOUSE</span>
                ) : (
                  <span className="text-blue-600">BUY SAAS</span>
                )}
              </div>
              <p className="text-gray-600">
                {results.isBuildCheaper ? (
                  <>
                    Building in-house saves{' '}
                    <span className="font-semibold text-green-600">
                      ${results.costDifference.toLocaleString()}
                    </span>{' '}
                    over 3 years
                  </>
                ) : (
                  <>
                    Buying SaaS saves{' '}
                    <span className="font-semibold text-blue-600">
                      ${results.costDifference.toLocaleString()}
                    </span>{' '}
                    over 3 years
                  </>
                )}
              </p>
            </div>
          </div>

          {/* Cost Breakdown */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">3-Year Cost Breakdown</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="font-semibold text-lg">Build In-House</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Initial Development:</span>
                    <span>${results.initialBuildCost.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Yearly Maintenance:</span>
                    <span>${results.yearlyMaintenanceCost.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Yearly Infrastructure:</span>
                    <span>${results.yearlyInfrastructureCost.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between font-semibold border-t pt-2">
                    <span>3-Year Total:</span>
                    <span>${results.threeYearBuildCost.toLocaleString()}</span>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="font-semibold text-lg">Buy SaaS</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Yearly Subscription:</span>
                    <span>${results.yearlySubscriptionCost.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Per User/Month:</span>
                    <span>${results.monthlyPerUserSaasCost.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between font-semibold border-t pt-2">
                    <span>3-Year Total:</span>
                    <span>${results.threeYearSaasCost.toLocaleString()}</span>
                  </div>
                </div>
              </div>
            </div>

            {results.breakEvenMonths > 0 && results.breakEvenMonths < 120 && (
              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600">
                  <strong>Break-even point:</strong> {Math.round(results.breakEvenMonths)} months
                </p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
