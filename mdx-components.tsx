import type { MDXComponents } from 'mdx/types';
import Image from 'next/image';
import Link from 'next/link';

// This file allows you to provide custom React components
// to be used in MDX files. You can import and use any
// React component you want, including components from
// other libraries.

export function useMDXComponents(components: MDXComponents): MDXComponents {
  return {
    // Allows customizing built-in components, e.g. to add styling.
    h1: ({ children }) => (
      <h1 className="font-bold text-4xl text-slate-900 tracking-tight sm:text-5xl dark:text-slate-100">
        {children}
      </h1>
    ),
    h2: ({ children }) => (
      <h2 className="font-bold text-3xl text-slate-900 tracking-tight sm:text-4xl dark:text-slate-100">
        {children}
      </h2>
    ),
    h3: ({ children }) => (
      <h3 className="font-bold text-2xl text-slate-900 tracking-tight sm:text-3xl dark:text-slate-100">
        {children}
      </h3>
    ),
    a: ({ href, children }) => {
      const isExternal = href?.startsWith('http');
      if (isExternal) {
        return (
          <a
            className="text-emerald-500 hover:underline"
            href={href}
            rel="noopener noreferrer"
            target="_blank"
          >
            {children}
          </a>
        );
      }
      return (
        <Link className="text-emerald-500 hover:underline" href={href || '/'}>
          {children}
        </Link>
      );
    },
    img: ({ src, alt }) => (
      <Image
        alt={alt || ''}
        className="rounded-lg"
        height={480}
        src={src || ''}
        width={720}
      />
    ),
    // Enhanced code block styling
    pre: ({ children }) => (
      <pre className="my-6 overflow-x-auto rounded-lg border border-slate-200 bg-slate-50 p-4 text-sm shadow-sm dark:border-slate-600 dark:bg-slate-900 dark:shadow-slate-900/20">
        {children}
      </pre>
    ),
    code: ({ children }) => (
      <code className="rounded bg-slate-100 px-1.5 py-0.5 font-mono text-slate-800 text-sm dark:bg-slate-800 dark:text-slate-200">
        {children}
      </code>
    ),
    // Add table components with refined styling
    table: ({ children }) => (
      <div className="my-6 overflow-x-auto">
        <table className="min-w-full border-collapse rounded-lg border border-slate-200 bg-white shadow-sm dark:border-slate-600 dark:bg-slate-900 dark:shadow-slate-900/20">
          {children}
        </table>
      </div>
    ),
    thead: ({ children }) => (
      <thead className="bg-slate-50 dark:bg-slate-800">{children}</thead>
    ),
    tbody: ({ children }) => (
      <tbody className="divide-y divide-slate-100 dark:divide-slate-700">
        {children}
      </tbody>
    ),
    tr: ({ children }) => (
      <tr className="transition-colors hover:bg-slate-50/50 dark:hover:bg-slate-800/60">
        {children}
      </tr>
    ),
    th: ({ children }) => (
      <th className="border-slate-200 border-b px-3 py-2 text-left font-medium text-slate-600 text-xs uppercase tracking-wider dark:border-slate-600 dark:text-slate-200">
        {children}
      </th>
    ),
    td: ({ children }) => (
      <td className="border-slate-100 border-b px-3 py-2 text-slate-500 text-sm dark:border-slate-700 dark:text-slate-300">
        {children}
      </td>
    ),
    // Add custom components
    ...components,
  };
}
