import type { Person } from './types';

export const inspirations: Person[] = [
  {
    name: '<PERSON>',
    url: 'https://twitter.com/awilk<PERSON>on',
    description: 'Founder of MetaLab, Tiny',
  },
  {
    name: '<PERSON>',
    url: 'https://benissen.com/<PERSON>+issen',
    description: 'Growth expert',
  },
  {
    name: '<PERSON>',
    url: 'https://www.gatesnotes.com/',
    description: 'Co-founder of Microsoft',
  },
  {
    name: '<PERSON>',
    url: 'https://backlinko.com/',
    description: 'SEO expert, founder of Backlinko',
  },
  {
    name: '<PERSON>',
    url: 'https://www.corey.co/',
    description: 'Founder of Swipewell',
  },
  {
    name: '<PERSON>',
    url: 'https://dvassallo.com/',
    description: 'Author, indie hacker',
  },
  {
    name: '<PERSON>',
    url: 'https://sive.rs/',
    description: 'Founder of CD Baby, author',
  },
  {
    name: '<PERSON>',
    url: 'https://ejorgenson.com/',
    description: 'Author, business builder',
  },
  {
    name: '<PERSON>',
    url: 'https://twitter.com/george__mack',
    description: 'Mental models expert',
  },
  {
    name: '<PERSON>sopp',
    url: 'https://detailed.com/',
    description: 'SEO expert, founder',
  },
  {
    name: 'James Currier',
    url: 'https://www.nfx.com/team/james-currier',
    description: 'NFX co-founder, network effects expert',
  },
  {
    name: '<PERSON>on',
    url: 'https://www.dyson.com/about-dyson/our-people/james-dyson',
    description: 'Inventor, founder of Dyson',
  },
  {
    name: 'Jeff Bezos',
    url: 'https://en.wikipedia.org/wiki/Jeff_Bezos',
    description: 'Founder of Amazon',
  },
  {
    name: 'Julian Shapiro',
    url: 'https://www.julian.com/',
    description: 'Writer, founder, angel investor',
  },
  {
    name: 'Luca Dellanna',
    url: 'https://dellanna.com/',
    description: 'Author, systems thinker',
  },
  {
    name: 'Morgan Housel',
    url: 'https://www.collaborativefund.com/blog/authors/morgan/',
    description: 'Author, investor, storyteller',
  },
  {
    name: 'Nat Eliason',
    url: 'https://www.nateliason.com/',
    description: 'Writer, founder',
  },
  {
    name: 'Nathan Barry',
    url: 'https://nathanbarry.com/',
    description: 'Founder of ConvertKit',
  },
  {
    name: 'Pat Walls',
    url: 'https://patwalls.com/',
    description: 'Founder of StarterStory',
  },
  {
    name: 'Paul Graham',
    url: 'http://paulgraham.com',
    description: 'YC co-founder, Viaweb founder, essayist',
  },
  {
    name: 'Pieter Levels',
    url: 'https://levels.io/',
    description: 'Founder of Nomad List, RemoteOK',
  },
  {
    name: 'Ray Dalio',
    url: 'https://www.principles.com/',
    description: 'Founder of Bridgewater Associates',
  },
  {
    name: 'Richard Branson',
    url: 'https://www.virgin.com/branson-family/richard-branson-blog',
    description: 'Founder of Virgin Group',
  },
  {
    name: 'Rob Walling',
    url: 'https://robwalling.com/',
    description: 'Founder of TinySeed, MicroConf',
  },
  {
    name: 'Ross Simmonds',
    url: 'https://rosssimmonds.com/',
    description: 'Founder of Foundation Marketing',
  },
  {
    name: 'Steph Smith',
    url: 'https://stephsmith.io/',
    description: 'Creator, writer, indie hacker',
  },
  {
    name: 'Steve Jobs',
    url: 'https://en.wikipedia.org/wiki/Steve_Jobs',
    description: 'Co-founder of Apple and Pixar',
  },
  {
    name: 'Thomas Edison',
    url: 'https://en.wikipedia.org/wiki/Thomas_Edison',
    description: 'Inventor, businessman, founded GE',
  },
];
