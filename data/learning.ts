import type { Learning } from './types';

export const learningItems: Learning[] = [
  {
    title: 'Next.js',
    url: 'https://nextjs.org/',
    description: 'React framework for production-grade applications',
  },
  {
    title: 'OpenAI',
    url: 'https://platform.openai.com/docs',
    description: 'AI/ML integration and development',
  },
  {
    title: 'Programmatic SEO',
    url: 'https://practicalprogrammatic.com/',
    description: 'Scaling content and traffic with programmatic SEO',
  },
  {
    title: 'Mastra',
    url: 'https://mastra.ai/',
    description: 'The TypeScript AI agent framework',
  },
  {
    title: 'SaaS Development',
    url: 'https://achromatic.dev/',
    description: 'Building scalable software services',
  },
];
