import type { MustReread } from './types';

export const mustRereadList: MustReread[] = [
  {
    title: 'High Agency In 30 Minutes',
    url: 'https://www.highagency.com/',
    author: '<PERSON>',
  },
  {
    title: '50 First Levers',
    url: 'https://www.ejorgenson.com/blog/50-first-levers',
    author: '<PERSON>',
  },
  {
    title: 'How to Be Successful',
    url: 'https://blog.samaltman.com/how-to-be-successful',
    author: '<PERSON>',
  },
  {
    title: 'How to Do Great Work',
    url: 'https://paulgraham.com/greatwork.html',
    author: '<PERSON>',
  },
  {
    title:
      'How to Manage Opportunity Cost: Attention Thresholds in Personal Wealth Building',
    url: 'https://www.ejorgenson.com/blog/opportunity-cost-attention-thresholds',
    author: '<PERSON>',
  },
  {
    title: 'Manual Work is a Bug',
    url: 'https://queue.acm.org/detail.cfm?id=3197520',
    author: 'ACM Queue',
  },
  {
    title: 'Personal Proximity to Profits',
    url: 'https://www.ejorgenson.com/blog/proximity-to-profits-business-ownership-scale-or-personal-value-capture',
    author: '<PERSON> <PERSON>',
  },
  {
    title: 'The <PERSON>-and-Good Feedback <PERSON>',
    url: 'https://www.ejorgenson.com/blog/the-lucky-and-good-feedback-loop',
    author: '<PERSON> <PERSON><PERSON>',
  },
  {
    title: 'The Mom Test',
    url: 'https://www.momtestbook.com/',
    author: 'Rob Fitz<PERSON>',
  },
];
