// Types
export interface Work {
  title: string;
  company: string;
  url: string;
  description: string;
}

export interface Project {
  title: string;
  url: string;
  description: string;
  year: string;
  status: 'Active' | 'Sold';
  nofollow?: boolean;
}

export interface Learning {
  title: string;
  url: string;
  description: string;
}

export interface Podcast {
  title: string;
  url: string;
  description: string;
  host: string;
}

export interface Book {
  title: string;
  author: string;
  url: string;
  description: string;
}

export interface Interest {
  title: string;
  url: string;
  description: string;
}

export interface Interview {
  title: string;
  url: string;
  description: string;
  year: string;
}

export interface Tool {
  title: string;
  url: string;
  description: string;
  pricing: string;
}

export interface MustReread {
  title: string;
  url?: string;
  author?: string;
}

export interface Person {
  name: string;
  url: string;
  description: string;
}

export interface Friend {
  name: string;
  url: string;
  description: string;
}

export interface Course {
  title: string;
  url: string;
  description: string;
  instructor: string;
}

export interface Event {
  title: string;
  url: string;
  description: string;
  attended?: boolean;
  wishlist?: boolean;
  year?: string;
}

// Data
export const workItems: Work[] = [
  {
    title: 'Partner',
    company: 'Craftled',
    url: 'https://craftled.com',
    description:
      'Building a portfolio of small bets in software and digital products',
  },
  {
    title: 'Founder',
    company: 'Makerkit',
    url: 'https://makerkit.dev',
    description:
      'The next-generation SaaS Starter Kit for ambitious developers',
  },
  {
    title: 'Founder',
    company: 'Achromatic',
    url: 'https://achromatic.dev',
    description:
      'Building software services with a focus on developer experience',
  },
];

export { projects } from './projects';

export const learningItems: Learning[] = [
  {
    title: 'Next.js',
    url: 'https://nextjs.org/',
    description: 'The hottest React framework for creating full-stack web apps',
  },
  {
    title: 'Makerkit',
    url: 'https://makerkit.dev/',
    description:
      'The next-generation SaaS Starter Kit for ambitious developers',
  },
  {
    title: 'OpenAI',
    url: 'https://openai.com/',
    description:
      'Learning about prompt engineering and how to use Al and ChatGPT effectively',
  },
];

export const podcasts: Podcast[] = [
  {
    title: 'Founders',
    host: 'David Senra',
    url: 'https://www.founderspodcast.com/',
    description: 'Life and business lessons from remarkable company builders',
  },
  {
    title: 'The Drive',
    host: 'Peter Attia',
    url: 'https://peterattiamd.com/podcast/',
    description: 'Deep insights on health, longevity and performance',
  },
  {
    title: 'Huberman Lab',
    host: 'Andrew Huberman',
    url: 'https://www.hubermanlab.com/podcast',
    description: 'Science-based tools for everyday life',
  },
];

export const books: Book[] = [
  {
    title: 'Zero to One',
    author: 'Peter Thiel',
    url: 'https://zerotoonebook.com/',
    description: 'Notes on startups, or how to build the future',
  },
  {
    title: 'The Mom Test',
    author: 'Rob Fitzpatrick',
    url: 'http://momtestbook.com/',
    description:
      'How to talk to customers & learn if your business is a good idea',
  },
  {
    title: 'Company of One',
    author: 'Paul Jarvis',
    url: 'https://ofone.co/',
    description: 'Why staying small is the next big thing for business',
  },
];

export const interests: Interest[] = [
  {
    title: 'Books',
    url: 'https://www.goodreads.com/author/show/14358907.Tomas_Laurinavicius',
    description: 'Reading and writing books.',
  },
  {
    title: 'Indie Hacking',
    url: 'https://www.indiehackers.com/tomaslau',
    description: 'Building indie businesses.',
  },
  {
    title: 'Traveling',
    url: 'https://nomads.com/@tomaslau',
    description: '50+ countries.',
  },
];

export const interviews: Interview[] = [
  {
    title: 'Zapier',
    url: 'https://makerpad.zapier.com/posts/member-spotlight-tomas-laurinavicius',
    description: 'Member Spotlight: Tomas Laurinavicius',
    year: '2021',
  },
  {
    title: 'Webflow',
    url: 'https://webflow.com/blog/how-to-create-a-job-board',
    description: 'How to create (and grow) a job board using Webflow',
    year: '2020',
  },
];

export const tools: Tool[] = [
  {
    title: 'Google Sheets',
    url: 'https://sheets.google.com/',
    description: 'Data analysis and organization',
    pricing: 'Free',
  },
  {
    title: 'Screaming Frog',
    url: 'https://www.screamingfrog.co.uk/seo-spider/',
    description: 'SEO analysis and auditing',
    pricing: '$259/yr',
  },
];

export const mustRereadList: MustReread[] = [
  {
    title: 'How to Do Great Work',
    url: 'https://paulgraham.com/greatwork.html',
    author: 'Paul Graham',
  },
  {
    title: 'Manual Work is a Bug',
    url: 'https://queue.acm.org/detail.cfm?id=3197520',
    author: 'ACM Queue',
  },
];

export const inspirations: Person[] = [
  {
    name: 'Paul Graham',
    url: 'http://paulgraham.com',
    description: 'YC co-founder, Viaweb founder, essayist',
  },
  {
    name: 'James Dyson',
    url: 'https://www.dyson.com/about-dyson/our-people/james-dyson',
    description: 'Inventor, founder of Dyson',
  },
];

export const friends: Friend[] = [
  {
    name: 'Edgaras Benediktavicius',
    url: 'https://edgaras.com/',
    description: 'Product designer and web developer',
  },
  {
    name: 'Chris Blahoot',
    url: 'https://www.thezag.com/',
    description: 'Founder of The Zag',
  },
];

export const courses: Course[] = [
  {
    title: 'Network Effects Masterclass',
    url: 'https://www.nfx.com/masterclass',
    description: 'Deep dive into network effects and marketplace dynamics',
    instructor: 'James Currier',
  },
  {
    title: 'Internet Pipes',
    url: 'https://internetpipes.com/',
    description: 'Understanding how the internet really works',
    instructor: 'Steph Smith',
  },
];

export const events: Event[] = [
  {
    title: 'ELEVATE Summit',
    url: 'https://www.affiversemedia.com/elevate-summit-affiliate-conference-london-2025-affiverse-media/',
    description: 'Lead generation and affiliate marketing conference in London',
    attended: true,
    year: '2024',
  },
  {
    title: 'saas.grouping',
    url: 'https://saas.group/',
    description: 'Annual saas.group meetup and conference',
    attended: true,
    year: '2023',
  },
];
