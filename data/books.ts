import type { Book } from './types';

export const books: Book[] = [
  {
    title: 'Make Something Wonderful',
    author: '<PERSON>',
    url: 'https://www.goodreads.com/book/show/128533513-make-something-wonderful',
    year: '2025',
  },
  {
    title: 'Generating Product Ideas',
    author: '<PERSON><PERSON><PERSON>',
    url: 'https://www.goodreads.com/book/show/54784082-generating-product-ideas',
    year: '2025',
  },
  {
    title: 'How to Be Rich',
    author: '<PERSON><PERSON>',
    url: 'https://www.goodreads.com/book/show/616693',
    year: '2025',
  },
  {
    title: "Alchemy: The Surprising Power of Ideas That Don't Make Sense",
    author: '<PERSON>',
    url: 'https://www.goodreads.com/book/show/26210508-alchemy',
    year: '2024',
  },
  {
    title: 'Becoming a Human Again',
    author: '<PERSON><PERSON>',
    url: 'https://www.goodreads.com/book/show/57506773-becoming-a-human-again',
    year: '2024',
  },
  {
    title:
      'Nexus: A Brief History of Information Networks from the Stone Age to AI',
    author: '<PERSON><PERSON>',
    url: 'https://www.goodreads.com/book/show/204927599-nexus',
    year: '2024',
  },
  {
    title: 'The 48 Laws of Power',
    author: 'Robert Greene',
    url: 'https://www.goodreads.com/book/show/1303.The_48_Laws_of_Power',
    year: '2024',
  },
  {
    title: 'The Mom Test',
    author: 'Rob Fitzpatrick',
    url: 'http://momtestbook.com/',
    year: '2024',
  },
  {
    title:
      'The SaaS Playbook: Build a Multimillion-Dollar Startup Without Venture Capital',
    author: 'Rob Walling',
    url: 'https://www.goodreads.com/book/show/178816351-the-saas-playbook',
    year: '2024',
  },
  {
    title: 'Optionality: How to Survive and Thrive in a Volatile World',
    author: 'Richard Meadows',
    url: 'https://www.goodreads.com/book/show/123202416-optionality',
    year: '2024',
  },
];
