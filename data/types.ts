// Common interfaces for all data types
export interface Work {
  company: string;
  title: string;
  url: string;
  description: string;
  year: string;
}

export interface Project {
  title: string;
  url: string;
  description: string;
  year: string;
  status: 'Active' | 'Sold' | 'Failed' | 'Discontinued';
  nofollow?: boolean;
}

export interface Learning {
  title: string;
  url: string;
  description: string;
}

export interface Podcast {
  title: string;
  url: string;
  description: string;
  host: string;
}

export interface Book {
  title: string;
  author: string;
  url: string;
  year?: string;
}

export interface Interview {
  title: string;
  url: string;
  description: string;
  year: string;
}

export interface Tool {
  title: string;
  url: string;
  description: string;
  pricing: string;
}

export interface MustReread {
  title: string;
  url?: string;
  author?: string;
}

export interface Person {
  name: string;
  url: string;
  description: string;
}

export interface Friend {
  name: string;
  url: string;
  description: string;
}

export interface Course {
  title: string;
  url: string;
  description: string;
  instructor: string;
}

export interface Event {
  title: string;
  url: string;
  description: string;
  attended?: boolean;
  wishlist?: boolean;
  year?: string;
}
