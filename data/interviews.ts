import type { Interview } from './types';

export const interviews: Interview[] = [
  {
    title: 'Z<PERSON><PERSON>',
    url: 'https://makerpad.zapier.com/posts/member-spotlight-to<PERSON>-laurina<PERSON><PERSON>',
    description: 'Member Spotlight: <PERSON>',
    year: '2021',
  },
  {
    title: 'Starter Story',
    url: 'https://www.starterstory.com/tomas-laurinavicius',
    description: 'On Helping Companies Streamline Their Content Production',
    year: '2020',
  },
  {
    title: 'Webflow',
    url: 'https://webflow.com/blog/how-to-create-a-job-board',
    description: 'How to create (and grow) a job board using Webflow',
    year: '2020',
  },
  {
    title: 'Typeform',
    url: 'https://www.typeform.com/blog/opinions-and-expertise/tomas-laurina<PERSON>-interview/',
    description:
      'Lost on a beach: an interview with designpreneur <PERSON>',
    year: '2019',
  },
  {
    title: 'Crowdfire',
    url: 'https://medium.com/crowdfire-product/veni-vidi-vici-a-tale-of-the-modern-day-conqueror-dd8c3c14bfcb',
    description: 'Veni, vidi, vici — A Tale of the Modern Day Conqueror',
    year: '2017',
  },
];
