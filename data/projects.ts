import type { Project } from './types';

export const projects: Project[] = [
  {
    title: 'Growth Jobs',
    url: 'https://growthjobs.org',
    description: 'Job board for growth professionals.',
    year: '2025',
    status: 'Active',
  },
  {
    title: 'XD Guru',
    url: 'https://xdguru.com',
    description: 'Experience design resources, templates and freebies.',
    year: '2025',
    status: 'Active',
  },
  {
    title: 'UX Crush',
    url: 'https://uxcrush.com',
    description:
      'The biggest collection of curated templates and resources for Figma.',
    year: '2025',
    status: 'Active',
  },
  {
    title: 'Draftpen',
    url: 'https://draftpen.com',
    description: 'Craft beautiful shareable visuals from your content.',
    year: '2025',
    status: 'Discontinued',
  },
  {
    title: 'Bordful',
    url: 'https://bordful.com',
    description: 'Open-source job board starter kit.',
    year: '2024',
    status: 'Active',
  },
  {
    title: 'Pynions',
    url: 'https://pynions.com',
    description: 'Python powered marketing automation.',
    year: '2024',
    status: 'Discontinued',
  },
  {
    title: 'Marketful',
    url: 'https://marketful.com',
    description: 'Digital marketing learning center.',
    year: '2024',
    status: 'Active',
  },
  {
    title: 'UI Things',
    url: 'https://uithings.com',
    description: 'Free digital design learning center.',
    year: '2024',
    status: 'Active',
  },
  {
    title: 'YTX: YouTube-Based Content for X',
    url: 'https://web.archive.org/web/20240822202207/https://ytx.craftled.com/',
    description: 'Transform YouTube videos into X posts.',
    year: '2024',
    status: 'Discontinued',
  },
  {
    title: 'Best Writing',
    url: 'https://bestwriting.com',
    description: 'The all-in-one writing marketplace.',
    year: '2022',
    status: 'Active',
  },
  {
    title: 'Port Surfer',
    url: 'https://web.archive.org/web/20240228232213/https://portsurfer.com/',
    description:
      'Organizing publicly available information on airports, seaports, cruise ports, train stations, and bus terminals.',
    year: '2022-2024',
    status: 'Discontinued',
  },
  {
    title: 'Cleantechy',
    url: 'https://web.archive.org/web/20210119091431/https://cleantechy.com/',
    description:
      'Environmental, conservation, and sustainability jobs and resources for greener careers.',
    year: '2020-2022',
    status: 'Discontinued',
  },
  {
    title: 'Secret Santa Friend',
    url: 'https://devsolutely.com/projects/secret-santa-friend',
    description:
      'Secret Santa Friend is an app created to play Secret Santa in person or via email and exchange gifts.',
    year: '2020-2024',
    status: 'Discontinued',
  },
  {
    title: 'Life in White Noise',
    url: 'https://web.archive.org/web/20180818214633/https://lifeinwhitenoise.com/',
    description:
      'Two dudes talking about independent work/life and what it means to them.',
    year: '2018',
    status: 'Discontinued',
  },
  {
    title: 'Podkastas',
    url: 'https://web.archive.org/web/20150720005750/http://podkastas.lt/',
    description: 'One of the first Lithuanian podcasts.',
    year: '2015',
    status: 'Discontinued',
  },
  {
    title: 'Coffee for Air',
    url: 'https://web.archive.org/web/20141106075142/https://coffeeforair.com/',
    description: 'Coffee for Air plants a tree for every coffee package sold.',
    year: '2014',
    status: 'Discontinued',
  },
  {
    title: 'Mobile Design Book',
    url: 'https://www.amazon.com/Mobile-Design-Book-Paula-Borowska-ebook/dp/B00MNWKM1A',
    description:
      'A small book about mobile design I wrote with Paula Borowska.',
    year: '2014',
    status: 'Discontinued',
  },
  {
    title: 'Despreneur',
    url: 'https://despreneur.com/',
    description: 'Magazine for design entrepreneurs.',
    year: '2013-2016',
    status: 'Sold',
  },
  {
    title: 'Who Design Today',
    url: 'https://whodesigntoday.com/',
    description: 'Web design blog.',
    year: '2011',
    status: 'Sold',
    nofollow: true,
  },
  {
    title: 'Web Design Fan',
    url: 'https://webdesignfan.com',
    description: 'Web design blog.',
    year: '2009',
    status: 'Sold',
    nofollow: true,
  },
  {
    title: 'Iniwoo',
    url: 'https://iniwoo.net',
    description: 'Graphic design blog.',
    year: '2008',
    status: 'Sold',
    nofollow: true,
  },
];
