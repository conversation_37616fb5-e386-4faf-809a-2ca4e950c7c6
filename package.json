{"name": "<PERSON><PERSON><PERSON>", "version": "1.0", "private": true, "engines": {"node": ">=22.x", "bun": ">=1.2.0"}, "packageManager": "bun@latest", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "ultracite lint", "format": "ultracite format", "clean": "rm -rf .next && rm -rf node_modules/.cache"}, "dependencies": {"@mdx-js/loader": "^3.1.0", "@mdx-js/mdx": "^3.0.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "^15.4.7", "@phosphor-icons/react": "^2.1.10", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@types/mdx": "^2.0.13", "@types/vfile": "^3.0.2", "axios": "^1.10.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^4.1.0", "dotenv": "^17.2.0", "geist": "^1.4.2", "gray-matter": "^4.0.3", "lucide-react": "^0.540.0", "next": "^15.4.7", "next-mdx-remote": "^5.0.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "rehype-slug": "^6.0.0", "remark-gfm": "^4.0.1", "sharp": "^0.34.3", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "vfile": "^6.0.3", "zod": "^4.0.5"}, "devDependencies": {"@biomejs/biome": "^2.2.0", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@types/node": "^24.3.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/validator": "^13.15.2", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.1", "typescript": "^5.8.3", "ultracite": "^5.2.4"}}