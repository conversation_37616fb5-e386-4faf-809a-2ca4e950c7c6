'use client';
import axios from 'axios';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { z } from 'zod';

const emailSchema = z
  .string()
  .email({ message: 'Please enter a valid email address.' });

const validateEmail = (email: string) => {
  try {
    emailSchema.parse(email);
    return { valid: true, error: null };
  } catch (_error) {
    return { valid: false, error: 'Please enter a valid email address.' };
  }
};

export default function WidgetNewsletter() {
  const [email, setEmail] = useState('');
  const [isEmailValid, setIsEmailValid] = useState(true);
  const [emailError, setEmailError] = useState<string | null>(null);
  const [submissionStatus, setSubmissionStatus] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [userIP, setUserIP] = useState<string | null>(null);
  const [firstName, setFirstName] = useState('');

  useEffect(() => {
    const fetchIP = async () => {
      try {
        const response = await axios.get('https://api.ipify.org?format=json');
        setUserIP(response.data.ip);
      } catch (_error) {
        // Silently fail if IP detection doesn't work - not critical
      }
    };

    fetchIP();
  }, []);

  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const validationResult = validateEmail(email);
    const isValid = validationResult.valid;

    setIsEmailValid(isValid);
    setEmailError(validationResult.error);

    if (!(isValid && userIP)) {
      setSubmissionStatus('');
      return;
    }

    setIsLoading(true);
    const enchargeApiKey = process.env.NEXT_PUBLIC_ENCHARGE_API_KEY;
    const enchargeEndpoint = `https://ingest.encharge.io/v1/${enchargeApiKey}`;
    const enchargeData = {
      user: {
        email,
        ip: userIP,
        firstName, // Dynamically passed
      },
    };

    try {
      const response = await axios.post(enchargeEndpoint, enchargeData, {
        headers: {
          'Content-Type': 'application/json',
        },
      });
      if (response.status === 200) {
        setSubmissionStatus('success');
      } else {
        throw new Error('Failed to submit email');
      }
    } catch (_error) {
      setSubmissionStatus('error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    setIsEmailValid(true);
    setEmailError(null);
  };

  const handleFirstNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFirstName(e.target.value);
  };

  return (
    <div className="rounded-lg border border-slate-200 p-5 dark:border-slate-800 dark:bg-gradient-to-t dark:from-slate-800 dark:to-slate-800/30">
      <div className="mb-4 text-center">
        <div className="mb-1 font-sans font-semibold">Growthlog Newsletter</div>
        <p className="text-slate-500 text-sm dark:text-slate-400">
          Growth marketing ideas for founders, side project enthusiasts, and
          weekend makers.
        </p>
      </div>
      {submissionStatus === 'success' ? (
        <p
          aria-live="polite"
          className="mb-4 text-center text-green-700 text-sm"
          role="alert"
        >
          🎉 This thing works! Check your email for confirmation. May take 1-2
          minutes.
        </p>
      ) : (
        <form onSubmit={onSubmit}>
          <div className="mb-2">
            <label className="sr-only" htmlFor="firstName">
              What's your first name?
            </label>
            <input
              className="form-input w-full py-1"
              id="firstName"
              onChange={handleFirstNameChange}
              placeholder="What's your first name?"
              required
              type="text"
              value={firstName}
            />
          </div>
          <div className="mb-2">
            <label className="sr-only" htmlFor="newsletter">
              Your email…
            </label>
            <input
              aria-describedby="email-error"
              aria-invalid={!isEmailValid}
              className={`form-input w-full py-1 ${
                isEmailValid ? '' : 'border-red-500'
              }`}
              disabled={isLoading}
              id="newsletter"
              onChange={handleEmailChange}
              placeholder="Your email…"
              required
              type="email"
              value={email}
            />
            {!isEmailValid && emailError && (
              <p
                aria-live="assertive"
                className="text-center text-red-600 text-sm"
                id="email-error"
                role="alert"
              >
                {emailError}
              </p>
            )}
            {!(isEmailValid || emailError) && (
              <p
                aria-live="assertive"
                className="text-center text-red-600 text-sm"
                id="email-error"
                role="alert"
              >
                Please enter a valid email address.
              </p>
            )}
          </div>

          <button
            aria-live={submissionStatus === 'success' ? 'polite' : undefined}
            className="btn-sm w-full bg-emerald-500 text-slate-100 hover:bg-emerald-600"
            disabled={isLoading || submissionStatus === 'success'}
            type="submit"
          >
            {isLoading ? (
              <svg
                className="-ml-1 mr-2 h-4 w-4 animate-spin text-white"
                fill="none"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <title>Loading</title>
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                />
                <path
                  className="opacity-75"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-1.647zM20 12a8 8 0 01-8 8v-4c2.206 0 4-1.794 4-4h4zm-2-5.291A7.962 7.962 0 0120 12h4c0-3.042-1.135-5.824-3-7.938l-3 1.647z"
                  fill="currentColor"
                />
              </svg>
            ) : (
              'Subscribe'
            )}
          </button>
          <p className="mt-2 text-center text-slate-500 text-sm dark:text-slate-400">
            Past issues:{' '}
            <Link
              className="text-emerald-600 text-sm hover:underline dark:text-emerald-400"
              href="/newsletter/growthlog-5"
            >
              5
            </Link>
            ,{' '}
            <Link
              className="text-emerald-600 text-sm hover:underline dark:text-emerald-400"
              href="/newsletter/growthlog-4"
            >
              4
            </Link>
            ,{' '}
            <Link
              className="text-emerald-600 text-sm hover:underline dark:text-emerald-400"
              href="/newsletter/growthlog-3"
            >
              3
            </Link>
            ,{' '}
            <Link
              className="text-emerald-600 text-sm hover:underline dark:text-emerald-400"
              href="/newsletter/growthlog-2"
            >
              2
            </Link>
            ,{' '}
            <Link
              className="text-emerald-600 text-sm hover:underline dark:text-emerald-400"
              href="/newsletter/growthlog-1"
            >
              1
            </Link>
          </p>
        </form>
      )}
    </div>
  );
}
