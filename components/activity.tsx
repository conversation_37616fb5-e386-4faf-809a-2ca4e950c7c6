'use client';

import React from 'react';

// SVG icon component for the chevron down
const ChevronDownIcon: React.FC = () => (
  <svg
    fill="none"
    height="12"
    stroke="currentColor"
    strokeLinecap="round"
    strokeLinejoin="round"
    strokeWidth="2"
    viewBox="0 0 24 24"
    width="12"
    xmlns="http://www.w3.org/2000/svg"
  >
    <title>Toggle menu</title>
    <polyline points="6 9 12 15 18 9" />
  </svg>
);

const ContributionGraph: React.FC = () => {
  const months = [
    'Oct',
    'Nov',
    'Dec',
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
  ];
  const days = ['Mon', '', 'Wed', '', 'Fri', ''];

  // Generate mock data for 53 weeks (371 days)
  const contributions = React.useMemo(
    () =>
      new Array(53)
        .fill(null)
        .map(() =>
          new Array(7).fill(null).map(() => Math.floor(Math.random() * 5))
        ),
    []
  );

  const getContributionColor = React.useCallback((level: number) => {
    const colors = ['#161b22', '#0e4429', '#006d32', '#26a641', '#39d353'];
    return colors[level];
  }, []);

  return (
    <div className="bg-[#0d1117] p-4 font-sans text-[#c9d1d9]">
      <div className="mb-2 flex items-center justify-between">
        <h2 className="font-semibold text-base">
          838 contributions in the last year
        </h2>
        <button
          className="flex items-center text-[#8b949e] text-xs hover:text-[#c9d1d9]"
          type="button"
        >
          Contribution settings
          <ChevronDownIcon />
        </button>
      </div>
      <div className="rounded-md border border-[#30363d] p-4">
        <div className="mb-2 flex">
          <div className="w-[27px]" />
          <div className="flex flex-1 justify-between text-[#8b949e] text-xs">
            {months.map((month) => (
              <div key={month}>{month}</div>
            ))}
          </div>
        </div>
        <div className="flex">
          <div className="mr-2 flex flex-col justify-between text-[#8b949e] text-xs">
            {days.map((day) => (
              <div className="h-[15px] leading-[15px]" key={day}>
                {day}
              </div>
            ))}
          </div>
          <div className="flex-1">
            <div className="grid grid-flow-col grid-cols-53 grid-rows-7 gap-[2px]">
              {contributions.map((week, weekIndex) =>
                week.map((day, dayIndex) => (
                  <div
                    className="h-[10px] w-[10px] rounded-sm"
                    key={`week-${weekIndex}-day-${dayIndex}-contrib-${day}`}
                    style={{ backgroundColor: getContributionColor(day) }}
                    title={`${day} contributions`}
                  />
                ))
              )}
            </div>
          </div>
        </div>
        <div className="mt-2 flex items-center justify-between text-[#8b949e] text-xs">
          <button className="hover:text-[#c9d1d9]" type="button">
            Learn how we count contributions
          </button>
          <div className="flex items-center">
            <span className="mr-2">Less</span>
            <div className="flex gap-[2px]">
              {[0, 1, 2, 3, 4].map((level) => (
                <div
                  className="h-[10px] w-[10px] rounded-sm"
                  key={level}
                  style={{ backgroundColor: getContributionColor(level) }}
                />
              ))}
            </div>
            <span className="ml-2">More</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContributionGraph;
