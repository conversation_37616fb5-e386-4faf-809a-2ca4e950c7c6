'use client';

import Image from 'next/image';
import { useState } from 'react';
import Illustration from '@/public/images/features-illustration-02.svg';
import FeaturesImage from '@/public/images/features-image.png';

export default function Features02() {
  const [category, setCategory] = useState<string>('1');

  return (
    <section className="relative border-slate-800 border-t">
      {/* Bg gradient: top */}
      <div
        aria-hidden="true"
        className="-z-10 pointer-events-none absolute top-0 right-0 left-0 h-[25rem] bg-gradient-to-b from-slate-800 to-transparent opacity-25"
      />
      {/* Illustration */}
      <div
        aria-hidden="true"
        className="-translate-x-1/2 -z-10 pointer-events-none absolute top-0 left-1/2 mt-40 hidden lg:block"
      >
        <Image
          alt="Features 02 Illustration"
          className="max-w-none"
          height="453"
          src={Illustration}
          width="1440"
        />
      </div>
      <div className="mx-auto max-w-6xl px-4 sm:px-6">
        <div className="py-12 md:py-20">
          {/* Section header */}
          <div className="mx-auto max-w-3xl pb-12 text-center md:pb-20">
            <h2 className="h2 font-hkgrotesk">
              Features to help you create your best designs
            </h2>
          </div>
          {/* Box */}
          <div className="overflow-hidden rounded bg-slate-800 bg-opacity-60">
            <div className="flex flex-col items-end md:flex-row md:items-start md:justify-between lg:space-x-20">
              <div className="p-6 md:min-w-[28rem] lg:p-10">
                {/* Filters */}
                <div className="mb-6 lg:mb-8">
                  <div className="-m-1.5 flex flex-wrap">
                    <button
                      className={`btn-sm m-1.5 rounded-full px-3 py-1 shadow-sm ${
                        category === '1'
                          ? 'bg-indigo-500 text-white'
                          : 'border-slate-600 bg-slate-700 text-slate-300 hover:bg-slate-600'
                      }`}
                      onClick={() => setCategory('1')}
                      type="button"
                    >
                      Everyone
                    </button>
                    <button
                      className={`btn-sm m-1.5 rounded-full px-3 py-1 shadow-sm ${
                        category === '2'
                          ? 'bg-indigo-500 text-white'
                          : 'border-slate-600 bg-slate-700 text-slate-300 hover:bg-slate-600'
                      }`}
                      onClick={() => setCategory('2')}
                      type="button"
                    >
                      Freelancers
                    </button>
                    <button
                      className={`btn-sm m-1.5 rounded-full px-3 py-1 shadow-sm ${
                        category === '3'
                          ? 'bg-indigo-500 text-white'
                          : 'border-slate-600 bg-slate-700 text-slate-300 hover:bg-slate-600'
                      }`}
                      onClick={() => setCategory('3')}
                      type="button"
                    >
                      Organizations
                    </button>
                  </div>
                </div>
                {/* Content */}
                <div>
                  <div className={`${category !== '1' && 'hidden'}`}>
                    <h3 className="h3 mb-2 font-hkgrotesk">
                      Kickstart your project with these tools
                    </h3>
                    <div className="text-lg text-slate-500">
                      Duis aute irure dolor in reprehenderit in voluptate velit
                      esse cillum dolore eu fugiat nulla pariatur, excepteur
                      sint occaecat cupidatat non proident, sunt in culpa qui
                      officia deserunt laborum.
                    </div>
                  </div>
                  <div className={`${category !== '2' && 'hidden'}`}>
                    <h3 className="h3 mb-2 font-hkgrotesk">
                      Tech tools to kickstart freelance life
                    </h3>
                    <div className="text-lg text-slate-500">
                      Duis aute irure dolor in reprehenderit in voluptate velit
                      esse cillum dolore eu fugiat nulla pariatur, excepteur
                      sint occaecat cupidatat non proident, sunt in culpa qui
                      officia deserunt laborum.
                    </div>
                  </div>
                  <div className={`${category !== '3' && 'hidden'}`}>
                    <h3 className="h3 mb-2 font-hkgrotesk">
                      Share your plan and clarify project risk
                    </h3>
                    <div className="text-lg text-slate-500">
                      Duis aute irure dolor in reprehenderit in voluptate velit
                      esse cillum dolore eu fugiat nulla pariatur, excepteur
                      sint occaecat cupidatat non proident, sunt in culpa qui
                      officia deserunt laborum.
                    </div>
                  </div>
                </div>
              </div>
              <Image
                alt="Feature"
                className="md:max-w-none"
                height="414"
                src={FeaturesImage}
                width="480"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
