'use client';

import Link from 'next/link';
import { useState } from 'react';
import {
  type Book,
  books,
  type Course,
  type Event,
  type Friend,
  friends,
  type Interview,
  type Learning,
  type MustReread,
  type Person,
  type Podcast,
  type Project,
  projects,
  type Tool,
  type Work,
  workItems,
} from '@/data';
import type { Post } from '@/lib/mdx';

interface PostItemProps {
  slug: string;
  title: string;
  publishedAt: string | Date;
  updatedAt: string | Date;
  location?: string;
  image?: string;
  summary?: string;
  content: string;
}

interface WorkItemProps extends Work {}
interface ProjectItemProps extends Project {}
interface LearningItemProps extends Learning {}
interface PodcastItemProps extends Podcast {}
interface BookItemProps extends Book {}
interface InterviewItemProps extends Interview {}
interface ToolItemProps extends Tool {}
interface MustRereadItemProps extends MustReread {}
interface PersonItemProps extends Person {}
interface FriendItemProps extends Friend {}
interface CourseItemProps extends Course {}
interface EventItemProps extends Event {}

interface SectionsProps {
  posts: Post[];
}

const PostItem = ({ title, slug, publishedAt }: PostItemProps) => {
  const year = new Date(publishedAt).getFullYear();
  return (
    <div className="flex items-center justify-between py-2">
      <Link
        className="text-slate-800 text-sm transition-colors hover:text-slate-900 dark:text-slate-200 dark:hover:text-white"
        href={`/blog/${slug}`}
      >
        {title}
      </Link>
      <span className="rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] text-slate-500 dark:bg-slate-800 dark:text-slate-400">
        {year}
      </span>
    </div>
  );
};

const WorkItem = ({
  company,
  title,
  description,
  year,
  url,
}: WorkItemProps) => {
  const isActive = year.includes('Now');

  return (
    <div className="flex flex-wrap items-start justify-between gap-2 py-3 sm:flex-nowrap sm:items-center sm:py-2">
      <div className="flex w-full flex-col gap-1 sm:gap-2">
        <div className="flex flex-wrap items-center gap-2">
          <Link
            className="truncate text-slate-800 text-sm transition-colors hover:text-slate-900 dark:text-slate-200 dark:hover:text-white"
            href={url}
            rel="noopener"
            target="_blank"
          >
            {company}
          </Link>
          <span className="shrink-0 rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] text-slate-500 dark:bg-slate-800 dark:text-slate-400">
            {title}
          </span>
          <span
            className={`whitespace-nowrap rounded-full px-1.5 py-0.5 text-[10px] sm:hidden ${
              isActive
                ? 'bg-emerald-500/10 text-emerald-500'
                : 'bg-slate-100 text-slate-500 dark:bg-slate-800 dark:text-slate-400'
            }`}
          >
            {year}
          </span>
        </div>
        {description && (
          <span className="text-slate-500 text-xs dark:text-slate-400">
            {description}
          </span>
        )}
      </div>
      <span
        className={`hidden whitespace-nowrap rounded-full px-1.5 py-0.5 text-[10px] sm:inline ${
          isActive
            ? 'bg-emerald-500/10 text-emerald-500'
            : 'bg-slate-100 text-slate-500 dark:bg-slate-800 dark:text-slate-400'
        }`}
      >
        {year}
      </span>
    </div>
  );
};

const ProjectItem = ({
  title,
  description,
  url,
  year,
  status,
}: ProjectItemProps) => {
  return (
    <div className="flex flex-wrap items-start justify-between gap-2 py-3 sm:flex-nowrap sm:items-center sm:py-2">
      <div className="flex w-full flex-col gap-1 sm:gap-2">
        <div className="flex flex-wrap items-center gap-2">
          <Link
            className="truncate text-slate-800 text-sm transition-colors hover:text-slate-900 dark:text-slate-200 dark:hover:text-white"
            href={url}
            rel="noopener noreferrer"
            target="_blank"
          >
            {title}
          </Link>
          <div className="flex items-center gap-2">
            <span
              className={`shrink-0 rounded-full px-1.5 py-0.5 text-[10px] ${
                status === 'Active'
                  ? 'bg-emerald-50 text-emerald-500 dark:bg-emerald-500/10 dark:text-emerald-400'
                  : 'bg-slate-100 text-slate-500 dark:bg-slate-800 dark:text-slate-400'
              }`}
            >
              {status}
            </span>
            {year && (
              <span className="shrink-0 rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] text-slate-500 sm:hidden dark:bg-slate-800 dark:text-slate-400">
                {year}
              </span>
            )}
          </div>
        </div>
        {description && (
          <span className="text-slate-500 text-xs dark:text-slate-400">
            {description}
          </span>
        )}
      </div>
      {year && (
        <span className="hidden shrink-0 rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] text-slate-500 sm:inline dark:bg-slate-800 dark:text-slate-400">
          {year}
        </span>
      )}
    </div>
  );
};

const PodcastItem = ({ title, url, description, host }: PodcastItemProps) => {
  return (
    <div className="flex flex-col items-center justify-between space-y-2 py-3 sm:flex-row sm:space-y-0 sm:py-2">
      <div className="flex w-full flex-col space-y-1 sm:flex-row sm:items-center sm:space-x-2 sm:space-y-0">
        <Link
          className="whitespace-normal text-slate-800 text-sm transition-colors hover:text-slate-900 sm:whitespace-nowrap dark:text-slate-200 dark:hover:text-white"
          href={url}
          rel="noopener noreferrer"
          target="_blank"
        >
          {title}
        </Link>
        <div className="flex flex-col space-y-1 text-slate-500 text-xs sm:flex-row sm:items-center sm:space-x-2 sm:space-y-0 dark:text-slate-400">
          {host && <span className="whitespace-nowrap">by {host}</span>}
          {description && <span>{description}</span>}
        </div>
      </div>
    </div>
  );
};

const BookItem = ({ title, author, url, year }: BookItemProps) => {
  return (
    <div className="flex flex-wrap items-start justify-between gap-2 py-3 sm:flex-nowrap sm:items-center sm:py-2">
      <div className="flex min-w-0 flex-col items-start gap-1 sm:flex-row sm:items-center sm:gap-2">
        <Link
          className="truncate text-slate-800 text-sm transition-colors hover:text-slate-900 dark:text-slate-200 dark:hover:text-white"
          href={url}
          rel="noopener noreferrer"
          target="_blank"
        >
          {title}
        </Link>
        <div className="flex items-center gap-2 text-slate-500 text-xs dark:text-slate-400">
          {author && <span className="truncate">by {author}</span>}
          <span className="shrink-0 rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] sm:hidden dark:bg-slate-800">
            {year}
          </span>
        </div>
      </div>
      {year && (
        <span className="hidden shrink-0 rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] text-slate-500 sm:inline dark:bg-slate-800 dark:text-slate-400">
          {year}
        </span>
      )}
    </div>
  );
};

const InterviewItem = ({
  title,
  url,
  description,
  year,
}: InterviewItemProps) => {
  return (
    <div className="flex flex-wrap items-start justify-between gap-2 py-3 sm:flex-nowrap sm:items-center sm:py-2">
      <div className="flex w-full flex-col gap-1 sm:gap-2">
        <div className="flex flex-wrap items-center gap-2">
          <Link
            className="truncate text-slate-800 text-sm transition-colors hover:text-slate-900 dark:text-slate-200 dark:hover:text-white"
            href={url}
            rel="noopener noreferrer"
            target="_blank"
          >
            {title}
          </Link>
          {year && (
            <span className="shrink-0 rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] text-slate-500 sm:hidden dark:bg-slate-800 dark:text-slate-400">
              {year}
            </span>
          )}
        </div>
        {description && (
          <span className="text-slate-500 text-xs dark:text-slate-400">
            {description}
          </span>
        )}
      </div>
      {year && (
        <span className="hidden shrink-0 rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] text-slate-500 sm:inline dark:bg-slate-800 dark:text-slate-400">
          {year}
        </span>
      )}
    </div>
  );
};

const ToolItem = ({ title, url, description, pricing }: ToolItemProps) => {
  return (
    <div className="flex items-center justify-between py-2">
      <div className="flex items-center space-x-3">
        <Link
          className="text-slate-800 text-sm transition-colors hover:text-slate-900 dark:text-slate-200 dark:hover:text-white"
          href={url}
          rel="noopener noreferrer"
          target="_blank"
        >
          {title}
        </Link>
        <span className="text-slate-500 text-xs dark:text-slate-400">
          {description}
        </span>
      </div>
      <span
        className={`rounded-full px-1.5 py-0.5 text-[10px] ${
          pricing === 'Free'
            ? 'bg-emerald-100 text-emerald-600 dark:bg-emerald-800/30 dark:text-emerald-400'
            : 'bg-amber-100 text-amber-600 dark:bg-amber-800/30 dark:text-amber-400'
        }`}
      >
        {pricing}
      </span>
    </div>
  );
};

const MustRereadItem = ({ title, url, author }: MustRereadItemProps) => {
  return (
    <div className="flex items-center justify-between py-2">
      <div className="flex items-center space-x-3">
        {url ? (
          <Link
            className="text-slate-800 text-sm transition-colors hover:text-slate-900 dark:text-slate-200 dark:hover:text-white"
            href={url}
            rel="noopener noreferrer"
            target="_blank"
          >
            {title}
          </Link>
        ) : (
          <span className="text-slate-800 text-sm dark:text-slate-200">
            {title}
          </span>
        )}
        {author && (
          <span className="text-slate-500 text-xs dark:text-slate-400">
            by {author}
          </span>
        )}
      </div>
    </div>
  );
};

const PersonItem = ({ name, url, description }: PersonItemProps) => {
  return (
    <div className="flex items-center justify-between py-2">
      <div className="flex items-center space-x-3">
        <Link
          className="text-slate-800 text-sm transition-colors hover:text-slate-900 dark:text-slate-200 dark:hover:text-white"
          href={url}
          rel="noopener noreferrer"
          target="_blank"
        >
          {name}
        </Link>
        <span className="text-slate-500 text-xs dark:text-slate-400">
          {description}
        </span>
      </div>
    </div>
  );
};

const FriendItem = ({ name, url, description }: FriendItemProps) => {
  return (
    <div className="flex items-center justify-between py-2">
      <div className="flex items-center space-x-3">
        <Link
          className="text-slate-800 text-sm transition-colors hover:text-slate-900 dark:text-slate-200 dark:hover:text-white"
          href={url}
          rel="noopener noreferrer"
          target="_blank"
        >
          {name}
        </Link>
        <span className="text-slate-500 text-xs dark:text-slate-400">
          {description}
        </span>
      </div>
    </div>
  );
};

const CourseItem = ({
  title,
  url,
  description,
  instructor,
}: CourseItemProps) => {
  return (
    <div className="flex items-center justify-between py-2">
      <div className="flex items-center space-x-3">
        <Link
          className="text-slate-800 text-sm transition-colors hover:text-slate-900 dark:text-slate-200 dark:hover:text-white"
          href={url}
          rel="noopener noreferrer"
          target="_blank"
        >
          {title}
        </Link>
        <span className="text-slate-500 text-xs dark:text-slate-400">
          {description}
        </span>
      </div>
      <span className="rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] text-slate-500 dark:bg-slate-800 dark:text-slate-400">
        {instructor}
      </span>
    </div>
  );
};

const EventItem = ({
  title,
  url,
  description,
  attended,
  wishlist,
  year,
}: EventItemProps) => {
  return (
    <div className="flex flex-wrap items-start justify-between gap-2 py-3 sm:flex-nowrap sm:items-center sm:py-2">
      <div className="flex w-full flex-col gap-1 sm:gap-2">
        <div className="flex flex-wrap items-center gap-2">
          <Link
            className="truncate text-slate-800 text-sm transition-colors hover:text-slate-900 dark:text-slate-200 dark:hover:text-white"
            href={url}
            rel="noopener noreferrer"
            target="_blank"
          >
            {title}
          </Link>
          <div className="flex items-center gap-2">
            {attended && (
              <span className="shrink-0 rounded-full bg-emerald-50 px-1.5 py-0.5 text-[10px] text-emerald-500 dark:bg-emerald-500/10 dark:text-emerald-400">
                Attended
              </span>
            )}
            {wishlist && (
              <span className="shrink-0 rounded-full bg-amber-50 px-1.5 py-0.5 text-[10px] text-amber-500 dark:bg-amber-500/10 dark:text-amber-400">
                Wishlist
              </span>
            )}
            {year && (
              <span className="shrink-0 rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] text-slate-500 sm:hidden dark:bg-slate-800 dark:text-slate-400">
                {year}
              </span>
            )}
          </div>
        </div>
        {description && (
          <span className="text-slate-500 text-xs dark:text-slate-400">
            {description}
          </span>
        )}
      </div>
      {year && (
        <span className="hidden shrink-0 rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] text-slate-500 sm:inline dark:bg-slate-800 dark:text-slate-400">
          {year}
        </span>
      )}
    </div>
  );
};

const LearningItem = ({ title, url, description }: LearningItemProps) => {
  return (
    <div className="flex flex-col items-center justify-between space-y-2 py-3 sm:flex-row sm:space-y-0 sm:py-2">
      <div className="flex w-full flex-col space-y-1 sm:flex-row sm:items-center sm:space-x-2 sm:space-y-0">
        {url ? (
          <Link
            className="whitespace-normal text-slate-800 text-sm transition-colors hover:text-slate-900 sm:whitespace-nowrap dark:text-slate-200 dark:hover:text-white"
            href={url}
            rel="noopener noreferrer"
            target="_blank"
          >
            {title}
          </Link>
        ) : (
          <span className="whitespace-normal text-slate-800 text-sm sm:whitespace-nowrap dark:text-slate-200">
            {title}
          </span>
        )}
        {description && (
          <span className="text-slate-500 text-xs dark:text-slate-400">
            {description}
          </span>
        )}
      </div>
    </div>
  );
};

// biome-ignore lint/suspicious/noExplicitAny: Helper function needs to handle multiple item types dynamically
const getItemKey = (item: any): string => {
  return item.title || item.name || item.slug || item.url || 'unknown';
};

const RenderItems = <T,>({
  items,
  Component,
  section,
}: {
  items: T[];
  Component: React.ComponentType<T>;
  section: string;
}) => {
  const [expandedSections, setExpandedSections] = useState<
    Record<string, boolean>
  >({});

  const isExpanded = expandedSections[section];
  const displayItems = isExpanded ? items : items.slice(0, 5);
  const hasMore = items.length > 5;

  return (
    <>
      {displayItems.map((item) => (
        <Component key={getItemKey(item)} {...item} />
      ))}
      {hasMore && (
        <button
          className="mt-2 flex items-center rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] text-slate-500 transition-colors hover:bg-emerald-100 hover:text-emerald-600 dark:bg-slate-800 dark:text-slate-400 dark:hover:bg-emerald-800/30 dark:hover:text-emerald-400"
          onClick={() => {
            setExpandedSections({
              ...expandedSections,
              [section]: !isExpanded,
            });
          }}
          type="button"
        >
          {isExpanded ? 'Show Less' : `Show ${items.length - 5} More`}
        </button>
      )}
    </>
  );
};

export default function Sections({ posts }: SectionsProps) {
  const [_activeSection, _setActiveSection] = useState<string>('all');
  const [showAllWork, setShowAllWork] = useState(false);
  const [showAllProjects, setShowAllProjects] = useState(false);
  const [showAllPosts, setShowAllPosts] = useState(false);
  const [showAllBooks, setShowAllBooks] = useState(false);

  const recentPosts = posts.slice(0, 4);
  const displayedPosts = showAllPosts ? posts : recentPosts;

  const activeWorkItems = workItems.filter((item) => item.year.includes('Now'));
  const displayedWorkItems = showAllWork ? workItems : activeWorkItems;

  const activeProjects = projects.filter((item) => item.status === 'Active');
  const displayedProjects = showAllProjects ? projects : activeProjects;

  const recentBooks = books.slice(0, 5);
  const displayedBooks = showAllBooks ? books : recentBooks;

  return (
    <div className="space-y-16 pb-16">
      {/* Writing Section */}
      <div>
        <h2 className="mb-3 font-medium font-sans text-slate-500 text-xs dark:text-slate-400">
          Writing
        </h2>
        <div className="border-slate-200 border-t dark:border-slate-800">
          {displayedPosts.map((post) => (
            <PostItem key={post.slug} {...post} />
          ))}
          {posts.length > recentPosts.length && (
            <div className="flex items-center justify-between py-2">
              <button
                className="rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] text-slate-500 transition-colors hover:bg-slate-200 dark:bg-slate-800 dark:text-slate-400 dark:hover:bg-slate-700"
                onClick={() => setShowAllPosts(!showAllPosts)}
                type="button"
              >
                {showAllPosts
                  ? `Show recent (${recentPosts.length})`
                  : `Show all (${posts.length})`}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Work Section */}
      <div>
        <h2 className="mb-3 font-medium font-sans text-slate-500 text-xs dark:text-slate-400">
          Work
        </h2>
        <div className="border-slate-200 border-t dark:border-slate-800">
          {displayedWorkItems.map((item) => (
            <WorkItem key={item.company || item.title} {...item} />
          ))}
          {workItems.length > activeWorkItems.length && (
            <div className="flex items-center justify-between py-2">
              <button
                className="rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] text-slate-500 transition-colors hover:bg-slate-200 dark:bg-slate-800 dark:text-slate-400 dark:hover:bg-slate-700"
                onClick={() => setShowAllWork(!showAllWork)}
                type="button"
              >
                {showAllWork
                  ? `Show active (${activeWorkItems.length})`
                  : `Show all (${workItems.length})`}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Projects Section */}
      <div>
        <h2 className="mb-3 font-medium font-sans text-slate-500 text-xs dark:text-slate-400">
          Projects
        </h2>
        <div className="border-slate-200 border-t dark:border-slate-800">
          {displayedProjects.map((item) => (
            <ProjectItem key={item.title} {...item} />
          ))}
          {projects.length > activeProjects.length && (
            <div className="flex items-center justify-between py-2">
              <button
                className="rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] text-slate-500 transition-colors hover:bg-slate-200 dark:bg-slate-800 dark:text-slate-400 dark:hover:bg-slate-700"
                onClick={() => setShowAllProjects(!showAllProjects)}
                type="button"
              >
                {showAllProjects
                  ? `Show active (${activeProjects.length})`
                  : `Show all (${projects.length})`}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Books Section */}
      <div>
        <h2 className="mb-3 font-medium font-sans text-slate-500 text-xs dark:text-slate-400">
          Reading List
        </h2>
        <div className="border-slate-200 border-t dark:border-slate-800">
          {displayedBooks.map((item) => (
            <BookItem key={item.title} {...item} />
          ))}
          {books.length > recentBooks.length && (
            <div className="flex items-center justify-between py-2">
              <button
                className="rounded-full bg-slate-100 px-1.5 py-0.5 text-[10px] text-slate-500 transition-colors hover:bg-slate-200 dark:bg-slate-800 dark:text-slate-400 dark:hover:bg-slate-700"
                onClick={() => setShowAllBooks(!showAllBooks)}
                type="button"
              >
                {showAllBooks
                  ? `Show recent (${recentBooks.length})`
                  : `Show all (${books.length})`}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Friends Section */}
      <div>
        <h2 className="mb-3 font-medium font-sans text-slate-500 text-xs dark:text-slate-400">
          Friends
        </h2>
        <div className="border-slate-200 border-t dark:border-slate-800">
          <RenderItems<Friend>
            Component={FriendItem}
            items={friends}
            section="friends"
          />
        </div>
      </div>
    </div>
  );
}

export {
  WorkItem,
  ProjectItem,
  LearningItem,
  PodcastItem,
  BookItem,
  InterviewItem,
  ToolItem,
  MustRereadItem,
  PersonItem,
  FriendItem,
  CourseItem,
  EventItem,
  RenderItems,
};
