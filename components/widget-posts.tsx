import Link from 'next/link';
import { getAllPosts } from '@/lib/mdx';

export default async function WidgetPosts() {
  const posts = await getAllPosts();

  return (
    <div className="dark:href-slate-800/30 even:-rotate-1 rounded-lg border border-slate-200 p-5 odd:rotate-1 dark:border-slate-800 dark:bg-gradient-href-t dark:from-slate-800">
      <div className="mb-3 font-sans font-semibold">Popular Posts</div>
      <ul className="space-y-3">
        {posts.slice(0, 5).map((post) => (
          <li className="inline-flex" key={post.slug}>
            <span className="mr-2 text-emerald-500">—</span>{' '}
            <Link
              className="before:-z-10 before:-rotate-2 relative inline-flex font-sans font-semibold text-sm duration-150 ease-out before:absolute before:inset-0 before:origin-center before:translate-y-1/4 before:scale-x-0 before:bg-emerald-200 before:opacity-30 before:duration-150 before:ease-in-out hover:text-emerald-500 hover:before:scale-100 dark:before:bg-emerald-500"
              href={`/blog/${post.slug}`}
            >
              {post.title}
            </Link>
          </li>
        ))}
      </ul>
    </div>
  );
}
