'use client';

import type React from 'react';
import { useEffect, useState } from 'react';

const ALICANTE_COORDS = { lat: 38.3452, lon: -0.4815 };

const calculateDistance = (
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number => {
  const R = 6371; // Radius of the Earth in km
  const dLat = ((lat2 - lat1) * Math.PI) / 180;
  const dLon = ((lon2 - lon1) * Math.PI) / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((lat1 * Math.PI) / 180) *
      Math.cos((lat2 * Math.PI) / 180) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

const LocationDistance: React.FC = () => {
  const [distance, setDistance] = useState<number | string | null>(null);

  useEffect(() => {
    const fetchLocation = async () => {
      try {
        const response = await fetch('https://ipapi.co/json/');
        const data = await response.json();

        if (data.latitude && data.longitude) {
          const calculatedDistance = calculateDistance(
            ALICANTE_COORDS.lat,
            ALICANTE_COORDS.lon,
            data.latitude,
            data.longitude
          );
          setDistance(Math.round(calculatedDistance));
        } else {
          setDistance('Unable to calculate');
        }
      } catch (_error) {
        setDistance('Unable to calculate');
      }
    };

    fetchLocation();
  }, []);

  return (
    <span className="ml-2 text-gray-500">
      {distance !== null
        ? `· ${distance} km away from you`
        : '· Calculating...'}
    </span>
  );
};

export default LocationDistance;
