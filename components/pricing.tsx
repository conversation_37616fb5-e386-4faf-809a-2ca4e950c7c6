'use client';

import Image from 'next/image';
import { useState } from 'react';
import Illustration from '@/public/images/pricing-illustration.svg';

export default function Pricing() {
  const [annual, setAnnual] = useState<boolean>(true);

  return (
    <section className="relative">
      {/* Illustration */}
      <div
        aria-hidden="true"
        className="-translate-x-1/2 -mb-24 -z-10 pointer-events-none absolute bottom-0 left-1/2 hidden lg:block"
      >
        <Image
          alt="Pricing Illustration"
          className="max-w-none"
          src={Illustration}
        />
      </div>
      <div className="mx-auto max-w-6xl px-4 sm:px-6">
        <div className="pt-10 pb-12 md:pb-20">
          {/* Section header */}
          <div className="mx-auto max-w-3xl pb-12 text-center md:pb-20">
            <h2 className="h2 mb-4 font-hkgrotesk">
              Let's find the right plan for you business
            </h2>
            <p className="text-slate-500 text-xl">
              Excepteur sint occaecat cupidatat non proident, sunt in culpa qui
              officia deserunt mollit anim id est.
            </p>
          </div>
          {/* Pricing tables */}
          <div className="grid md:grid-cols-6">
            {/* Pricing toggle */}
            <div className="flex flex-col justify-center bg-slate-800 p-4 md:col-span-3 md:px-6">
              <div className="flex items-center justify-center space-x-4 md:justify-start">
                <div className="min-w-[6rem] text-right font-medium text-slate-500 text-sm md:min-w-0">
                  Monthly
                </div>
                <div className="form-switch shrink-0">
                  <input
                    checked={annual}
                    className="sr-only"
                    id="toggle"
                    onChange={() => setAnnual(!annual)}
                    type="checkbox"
                  />
                  <label className="bg-slate-900" htmlFor="toggle">
                    <span aria-hidden="true" className="bg-slate-200" />
                    <span className="sr-only">Pay annually</span>
                  </label>
                </div>
                <div className="min-w-[6rem] font-medium text-slate-500 text-sm">
                  Yearly <span className="text-emerald-500">(-20%)</span>
                </div>
              </div>
            </div>
            {/* Starter price */}
            <div className="order-1 mt-6 flex flex-col justify-center border-slate-700 bg-slate-800 p-4 md:order-none md:mt-0 md:border-l md:px-6 md:text-center">
              <div className="mb-0.5 font-bold font-hkgrotesk text-indigo-500 text-lg">
                Starter
              </div>
              <div>
                <span className="font-semibold text-xl">$</span>
                <span className="font-semibold text-2xl">
                  {annual ? '29' : '35'}
                </span>
                <span className="font-medium text-slate-500 text-sm">/mo</span>
              </div>
            </div>
            {/* Agency price */}
            <div className="order-2 mt-6 flex flex-col justify-center border-slate-700 bg-slate-800 p-4 md:order-none md:mt-0 md:border-l md:px-6 md:text-center">
              <div className="mb-0.5 font-bold font-hkgrotesk text-indigo-500 text-lg">
                Agency
              </div>
              <div>
                <span className="font-semibold text-xl">$</span>
                <span className="font-semibold text-2xl">
                  {annual ? '49' : '55'}
                </span>
                <span className="font-medium text-slate-500 text-sm">/mo</span>
              </div>
            </div>
            {/* Team price */}
            <div className="order-3 mt-6 flex flex-col justify-center border-slate-700 bg-slate-800 p-4 md:order-none md:mt-0 md:border-l md:px-6 md:text-center">
              <div className="mb-0.5 font-bold font-hkgrotesk text-indigo-500 text-lg">
                Team
              </div>
              <div>
                <span className="font-semibold text-xl">$</span>
                <span className="font-semibold text-2xl">
                  {annual ? '79' : '85'}
                </span>
                <span className="font-medium text-slate-500 text-sm">/mo</span>
              </div>
            </div>
            {/* Usage label */}
            <div className="hidden flex-col justify-center bg-slate-700 bg-opacity-25 px-4 py-2 md:col-span-3 md:flex md:px-6">
              <span className="font-semibold text-slate-500 text-xs uppercase">
                Usage
              </span>
            </div>
            <div className="order-1 flex flex-col justify-center border-slate-700 bg-slate-700 bg-opacity-25 px-4 py-2 md:order-none md:border-l md:px-6">
              <span className="font-semibold text-slate-500 text-xs uppercase md:hidden">
                Usage
              </span>
            </div>
            <div className="order-2 flex flex-col justify-center border-slate-700 bg-slate-700 bg-opacity-25 px-4 py-2 md:order-none md:border-l md:px-6">
              <span className="font-semibold text-slate-500 text-xs uppercase md:hidden">
                Usage
              </span>
            </div>
            <div className="order-3 flex flex-col justify-center border-slate-700 bg-slate-700 bg-opacity-25 px-4 py-2 md:order-none md:border-l md:px-6">
              <span className="font-semibold text-slate-500 text-xs uppercase md:hidden">
                Usage
              </span>
            </div>
            {/* Admins & Members */}
            <div className="hidden flex-col justify-center bg-slate-800 p-4 md:col-span-3 md:flex md:px-6">
              <div className="text-slate-200">Admins &amp; Members</div>
            </div>
            <div className="order-1 flex justify-between border-slate-700 bg-slate-800 p-4 md:order-none md:flex-col md:justify-center md:border-l md:px-6">
              <div className="text-slate-200 md:hidden">
                Admins &amp; Members
              </div>
              <div className="text-center font-medium text-slate-200 text-sm">
                4
              </div>
            </div>
            <div className="order-2 flex justify-between border-slate-700 bg-slate-800 p-4 md:order-none md:flex-col md:justify-center md:border-l md:px-6">
              <div className="text-slate-200 md:hidden">
                Admins &amp; Members
              </div>
              <div className="text-center font-medium text-slate-200 text-sm">
                12
              </div>
            </div>
            <div className="order-3 flex justify-between border-slate-700 bg-slate-800 p-4 md:order-none md:flex-col md:justify-center md:border-l md:px-6">
              <div className="text-slate-200 md:hidden">
                Admins &amp; Members
              </div>
              <div className="text-center font-medium text-slate-200 text-sm">
                Unlimited
              </div>
            </div>
            {/* File Storage */}
            <div className="hidden flex-col justify-center bg-slate-800 bg-opacity-70 p-4 md:col-span-3 md:flex md:px-6">
              <div className="text-slate-200">File Storage</div>
            </div>
            <div className="order-1 flex justify-between border-slate-700 bg-slate-800 bg-opacity-70 p-4 md:order-none md:flex-col md:justify-center md:border-l md:px-6">
              <div className="text-slate-200 md:hidden">File Storage</div>
              <div className="text-center font-medium text-slate-200 text-sm">
                10GB
              </div>
            </div>
            <div className="order-2 flex justify-between border-slate-700 bg-slate-800 bg-opacity-70 p-4 md:order-none md:flex-col md:justify-center md:border-l md:px-6">
              <div className="text-slate-200 md:hidden">File Storage</div>
              <div className="text-center font-medium text-slate-200 text-sm">
                50GB
              </div>
            </div>
            <div className="order-3 flex justify-between border-slate-700 bg-slate-800 bg-opacity-70 p-4 md:order-none md:flex-col md:justify-center md:border-l md:px-6">
              <div className="text-slate-200 md:hidden">File Storage</div>
              <div className="text-center font-medium text-slate-200 text-sm">
                Unlimited
              </div>
            </div>
            {/* Active Users */}
            <div className="hidden flex-col justify-center bg-slate-800 p-4 md:col-span-3 md:flex md:px-6">
              <div className="text-slate-200">Active Users</div>
            </div>
            <div className="order-1 flex justify-between border-slate-700 bg-slate-800 p-4 md:order-none md:flex-col md:justify-center md:border-l md:px-6">
              <div className="text-slate-200 md:hidden">Active Users</div>
              <div className="text-center font-medium text-slate-200 text-sm">
                500
              </div>
            </div>
            <div className="order-2 flex justify-between border-slate-700 bg-slate-800 p-4 md:order-none md:flex-col md:justify-center md:border-l md:px-6">
              <div className="text-slate-200 md:hidden">Active Users</div>
              <div className="text-center font-medium text-slate-200 text-sm">
                1500
              </div>
            </div>
            <div className="order-3 flex justify-between border-slate-700 bg-slate-800 p-4 md:order-none md:flex-col md:justify-center md:border-l md:px-6">
              <div className="text-slate-200 md:hidden">Active Users</div>
              <div className="text-center font-medium text-slate-200 text-sm">
                Unlimited
              </div>
            </div>
            {/* Features label */}
            <div className="hidden flex-col justify-center bg-slate-700 bg-opacity-25 px-4 py-2 md:col-span-3 md:flex md:px-6">
              <span className="font-semibold text-slate-500 text-xs uppercase">
                Features
              </span>
            </div>
            <div className="order-1 flex flex-col justify-center border-slate-700 bg-slate-700 bg-opacity-25 px-4 py-2 md:order-none md:border-l md:px-6">
              <span className="font-semibold text-slate-500 text-xs uppercase md:hidden">
                Features
              </span>
            </div>
            <div className="order-2 flex flex-col justify-center border-slate-700 bg-slate-700 bg-opacity-25 px-4 py-2 md:order-none md:border-l md:px-6">
              <span className="font-semibold text-slate-500 text-xs uppercase md:hidden">
                Features
              </span>
            </div>
            <div className="order-3 flex flex-col justify-center border-slate-700 bg-slate-700 bg-opacity-25 px-4 py-2 md:order-none md:border-l md:px-6">
              <span className="font-semibold text-slate-500 text-xs uppercase md:hidden">
                Features
              </span>
            </div>
            {/* Unlimited Activities */}
            <div className="hidden flex-col justify-center bg-slate-800 p-4 md:col-span-3 md:flex md:px-6">
              <div className="text-slate-200">Unlimited Activities</div>
            </div>
            <div className="order-1 flex justify-between border-slate-700 bg-slate-800 p-4 md:order-none md:flex-col md:justify-center md:border-l md:px-6">
              <div className="text-slate-200 md:hidden">
                Unlimited Activities
              </div>
              <div className="text-center font-medium text-slate-200 text-sm">
                <svg
                  className="inline-flex fill-emerald-400"
                  height="9"
                  width="12"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>Included</title>
                  <path d="M10.28.28 3.989 6.575 1.695 4.28A1 1 0 0 0 .28 5.695l3 3a1 1 0 0 0 1.414 0l7-7A1 1 0 0 0 10.28.28Z" />
                </svg>
              </div>
            </div>
            <div className="order-2 flex justify-between border-slate-700 bg-slate-800 p-4 md:order-none md:flex-col md:justify-center md:border-l md:px-6">
              <div className="text-slate-200 md:hidden">
                Unlimited Activities
              </div>
              <div className="text-center font-medium text-slate-200 text-sm">
                <svg
                  className="inline-flex fill-emerald-400"
                  height="9"
                  width="12"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>Included</title>
                  <path d="M10.28.28 3.989 6.575 1.695 4.28A1 1 0 0 0 .28 5.695l3 3a1 1 0 0 0 1.414 0l7-7A1 1 0 0 0 10.28.28Z" />
                </svg>
              </div>
            </div>
            <div className="order-3 flex justify-between border-slate-700 bg-slate-800 p-4 md:order-none md:flex-col md:justify-center md:border-l md:px-6">
              <div className="text-slate-200 md:hidden">
                Unlimited Activities
              </div>
              <div className="text-center font-medium text-slate-200 text-sm">
                <svg
                  className="inline-flex fill-emerald-400"
                  height="9"
                  width="12"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>Included</title>
                  <path d="M10.28.28 3.989 6.575 1.695 4.28A1 1 0 0 0 .28 5.695l3 3a1 1 0 0 0 1.414 0l7-7A1 1 0 0 0 10.28.28Z" />
                </svg>
              </div>
            </div>
            {/* Data Export */}
            <div className="hidden flex-col justify-center bg-slate-800 bg-opacity-70 p-4 md:col-span-3 md:flex md:px-6">
              <div className="text-slate-200">Data Export</div>
            </div>
            <div className="order-1 flex justify-between border-slate-700 bg-slate-800 bg-opacity-70 p-4 md:order-none md:flex-col md:justify-center md:border-l md:px-6">
              <div className="text-slate-200 md:hidden">Data Export</div>
              <div className="text-center font-medium text-slate-200 text-sm">
                <svg
                  className="inline-flex fill-slate-500"
                  height="2"
                  width="14"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>Not included</title>
                  <path d="M14 0v2H0V0z" fillRule="evenodd" />
                </svg>
              </div>
            </div>
            <div className="order-2 flex justify-between border-slate-700 bg-slate-800 bg-opacity-70 p-4 md:order-none md:flex-col md:justify-center md:border-l md:px-6">
              <div className="text-slate-200 md:hidden">Data Export</div>
              <div className="text-center font-medium text-slate-200 text-sm">
                <svg
                  className="inline-flex fill-emerald-400"
                  height="9"
                  width="12"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>Included</title>
                  <path d="M10.28.28 3.989 6.575 1.695 4.28A1 1 0 0 0 .28 5.695l3 3a1 1 0 0 0 1.414 0l7-7A1 1 0 0 0 10.28.28Z" />
                </svg>
              </div>
            </div>
            <div className="order-3 flex justify-between border-slate-700 bg-slate-800 bg-opacity-70 p-4 md:order-none md:flex-col md:justify-center md:border-l md:px-6">
              <div className="text-slate-200 md:hidden">Data Export</div>
              <div className="text-center font-medium text-slate-200 text-sm">
                <svg
                  className="inline-flex fill-emerald-400"
                  height="9"
                  width="12"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>Included</title>
                  <path d="M10.28.28 3.989 6.575 1.695 4.28A1 1 0 0 0 .28 5.695l3 3a1 1 0 0 0 1.414 0l7-7A1 1 0 0 0 10.28.28Z" />
                </svg>
              </div>
            </div>
            {/* Adjust Group Sizes */}
            <div className="hidden flex-col justify-center bg-slate-800 p-4 md:col-span-3 md:flex md:px-6">
              <div className="text-slate-200">Adjust Group Sizes</div>
            </div>
            <div className="order-1 flex justify-between border-slate-700 bg-slate-800 p-4 md:order-none md:flex-col md:justify-center md:border-l md:px-6">
              <div className="text-slate-200 md:hidden">Adjust Group Sizes</div>
              <div className="text-center font-medium text-slate-200 text-sm">
                <svg
                  className="inline-flex fill-slate-500"
                  height="2"
                  width="14"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>Not included</title>
                  <path d="M14 0v2H0V0z" fillRule="evenodd" />
                </svg>
              </div>
            </div>
            <div className="order-2 flex justify-between border-slate-700 bg-slate-800 p-4 md:order-none md:flex-col md:justify-center md:border-l md:px-6">
              <div className="text-slate-200 md:hidden">Adjust Group Sizes</div>
              <div className="text-center font-medium text-slate-200 text-sm">
                <svg
                  className="inline-flex fill-emerald-400"
                  height="9"
                  width="12"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>Included</title>
                  <path d="M10.28.28 3.989 6.575 1.695 4.28A1 1 0 0 0 .28 5.695l3 3a1 1 0 0 0 1.414 0l7-7A1 1 0 0 0 10.28.28Z" />
                </svg>
              </div>
            </div>
            <div className="order-3 flex justify-between border-slate-700 bg-slate-800 p-4 md:order-none md:flex-col md:justify-center md:border-l md:px-6">
              <div className="text-slate-200 md:hidden">Adjust Group Sizes</div>
              <div className="text-center font-medium text-slate-200 text-sm">
                <svg
                  className="inline-flex fill-emerald-400"
                  height="9"
                  width="12"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>Included</title>
                  <path d="M10.28.28 3.989 6.575 1.695 4.28A1 1 0 0 0 .28 5.695l3 3a1 1 0 0 0 1.414 0l7-7A1 1 0 0 0 10.28.28Z" />
                </svg>
              </div>
            </div>
            {/* CTA row */}
            <div className="hidden flex-col justify-center bg-slate-700 bg-opacity-25 px-4 py-2 md:col-span-3 md:flex md:px-6" />
            <div className="order-1 flex flex-col justify-center border-slate-700 bg-slate-700 bg-opacity-25 p-4 md:order-none md:border-l">
              <a
                className="btn-sm group w-full whitespace-nowrap bg-indigo-500 text-white shadow-sm hover:bg-indigo-600"
                href="#0"
              >
                Free Trial{' '}
                <span className="ml-1 hidden text-emerald-300 tracking-normal transition-transform duration-150 ease-in-out group-hover:translate-x-0.5 lg:block">
                  -&gt;
                </span>
              </a>
            </div>
            <div className="order-2 flex flex-col justify-center border-slate-700 bg-slate-700 bg-opacity-25 p-4 md:order-none md:border-l">
              <a
                className="btn-sm group w-full whitespace-nowrap bg-indigo-500 text-white shadow-sm hover:bg-indigo-600"
                href="#0"
              >
                Free Trial{' '}
                <span className="ml-1 hidden text-emerald-300 tracking-normal transition-transform duration-150 ease-in-out group-hover:translate-x-0.5 lg:block">
                  -&gt;
                </span>
              </a>
            </div>
            <div className="order-3 flex flex-col justify-center border-slate-700 bg-slate-700 bg-opacity-25 p-4 md:order-none md:border-l">
              <a
                className="btn-sm group w-full whitespace-nowrap bg-indigo-500 text-white shadow-sm hover:bg-indigo-600"
                href="#0"
              >
                Free Trial{' '}
                <span className="ml-1 hidden text-emerald-300 tracking-normal transition-transform duration-150 ease-in-out group-hover:translate-x-0.5 lg:block">
                  -&gt;
                </span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
