export default function Interviews() {
  const interviews = [
    {
      title: 'Zapier',
      url: 'https://makerpad.zapier.com/posts/member-spotlight-to<PERSON>-laurina<PERSON><PERSON>',
      year: '2021',
    },
    {
      title: 'Webflow',
      url: 'https://webflow.com/blog/how-to-create-a-job-board',
      year: '2020',
    },
    {
      title: 'Starter Story',
      url: 'https://www.starterstory.com/tomas-laurinavicius',
      year: '2020',
    },
    {
      title: 'Typeform',
      url: 'https://www.typeform.com/blog/opinions-expertise/tomas-laurinavicius-interview/',
      year: '2019',
    },
    {
      title: 'Crowdfire',
      url: 'https://medium.com/crowdfire-product/veni-vidi-vici-a-tale-of-the-modern-day-conqueror-dd8c3c14bfcb',
      year: '2017',
    },
  ];

  return (
    <section>
      <h2 className="mb-6 font-geist font-semibold text-xl">Interviews</h2>
      <div className="space-y-1">
        {interviews.map((interview) => (
          <div className="py-2" key={interview.title}>
            <a
              className="group inline-flex items-baseline text-sm hover:text-gray-600 md:text-base"
              href={interview.url}
              rel="noopener noreferrer"
              target="_blank"
            >
              <span className="break-words font-medium group-hover:text-gray-600">
                {interview.title}
              </span>
              <span className="ml-2 shrink-0 text-gray-500 text-sm group-hover:text-gray-400">
                {interview.year}
              </span>
            </a>
          </div>
        ))}
      </div>
    </section>
  );
}
