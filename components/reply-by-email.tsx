interface ReplyByEmailProps {
  postTitle: string;
  className?: string;
}

export default function ReplyByEmail({
  postTitle,
  className = '',
}: ReplyByEmailProps) {
  const subject = `Reply to: ${postTitle}`;
  const mailtoUrl = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}`;

  return (
    <div className={`${className}`} style={{ marginTop: '2rem' }}>
      <a
        className="inline-flex items-center font-medium text-emerald-600 text-sm transition-colors duration-150 ease-in-out hover:text-emerald-700 dark:text-emerald-400 dark:hover:text-emerald-300"
        href={mailtoUrl}
      >
        <svg
          className="mr-2 h-4 w-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <title>Email reply</title>
          <path
            d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
          />
        </svg>
        Reply by email
      </a>
    </div>
  );
}
