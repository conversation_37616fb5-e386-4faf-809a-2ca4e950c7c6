'use client';

import {
  MDXRemote as ClientMDXRemote,
  type MDXRemoteSerializeResult,
} from 'next-mdx-remote';
import { serialize } from 'next-mdx-remote/serialize';
import { useEffect, useState } from 'react';
import rehypeSlug from 'rehype-slug';
import remarkGfm from 'remark-gfm';
import { useMDXComponents } from '@/mdx-components';

interface ClientMDXProps {
  source: string;
}

export default function ClientMDX({ source }: ClientMDXProps) {
  const [content, setContent] = useState<MDXRemoteSerializeResult | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function processMDX() {
      if (!source) {
        setError(new Error('MDX source is empty'));
        setLoading(false);
        return;
      }

      try {
        const mdxSource = await serialize(source, {
          parseFrontmatter: false,
          mdxOptions: {
            remarkPlugins: [remarkGfm],
            rehypePlugins: [rehypeSlug],
          },
        });
        setContent(mdxSource);
        setLoading(false);
      } catch (err) {
        setError(err instanceof Error ? err : new Error(String(err)));
        setLoading(false);
      }
    }

    processMDX();
  }, [source]);

  const components = useMDXComponents({});

  if (loading) {
    return (
      <div className="animate-pulse space-y-4">
        <div className="h-4 w-3/4 rounded bg-slate-100 dark:bg-slate-800" />
        <div className="h-4 rounded bg-slate-100 dark:bg-slate-800" />
        <div className="h-4 w-5/6 rounded bg-slate-100 dark:bg-slate-800" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="my-4 rounded-md border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-900/20">
        <p className="mb-2 font-medium text-red-600 dark:text-red-400">
          There was an error rendering the content.
        </p>
        {process.env.NODE_ENV === 'development' && (
          <pre className="overflow-auto rounded bg-red-100 p-2 text-red-500 text-xs dark:bg-red-900/30 dark:text-red-400">
            {error.message}
          </pre>
        )}
      </div>
    );
  }

  if (!content) {
    return (
      <p className="text-slate-500 dark:text-slate-400">
        No content available.
      </p>
    );
  }

  try {
    return <ClientMDXRemote {...content} components={components} />;
  } catch (_renderError) {
    return (
      <div className="my-4 rounded-md border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-900/20">
        <p className="mb-2 font-medium text-red-600 dark:text-red-400">
          Failed to render MDX content
        </p>
      </div>
    );
  }
}
