'use client';

import { TextIndent } from '@phosphor-icons/react';
import { useEffect, useState } from 'react';

interface TOCItem {
  id: string;
  title: string;
}

interface TableOfContentsProps {
  items: TOCItem[];
}

export default function PostTableOfContents({ items }: TableOfContentsProps) {
  const [activeId, setActiveId] = useState<string>('');

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        for (const entry of entries) {
          if (entry.isIntersecting) {
            setActiveId(entry.target.id);
          }
        }
      },
      { rootMargin: '0px 0px -80% 0px' }
    );

    for (const item of items) {
      const element = document.getElementById(item.id);
      if (element) {
        observer.observe(element);
      }
    }

    return () => {
      observer.disconnect();
    };
  }, [items]);

  if (items.length === 0) {
    return null;
  }

  return (
    <nav className="top-24 w-full rounded-lg bg-slate-100 p-4 dark:bg-slate-800">
      <h2 className="mb-3 flex items-center font-semibold text-slate-800 text-sm dark:text-slate-200">
        <TextIndent className="mr-2" size={16} />
        On this page
      </h2>
      <ul className="space-y-1.5">
        {items.map((item) => (
          <li key={item.id}>
            <a
              className={`relative block px-3 py-1 text-xs transition-colors duration-200 ${
                activeId === item.id
                  ? 'font-medium text-emerald-500'
                  : 'text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-200'
              }`}
              href={`#${item.id}`}
            >
              {activeId === item.id && (
                <span className="absolute top-0 bottom-0 left-0 w-0.5 rounded-full bg-emerald-500" />
              )}
              {item.title}
            </a>
          </li>
        ))}
      </ul>
    </nav>
  );
}
