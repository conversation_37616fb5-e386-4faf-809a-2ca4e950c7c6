'use client';

import { useEffect } from 'react';

const EnchargeWidget = () => {
  useEffect(() => {
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.className = 'encharge-form-embed-script';
    script.src = 'https://resources-app.encharge.io/embed-production.min.js';
    document.body.appendChild(script);
  }, []);

  return (
    <div
      className="encharge-form-embed"
      data-encharge-form-id="6c73f212-06e0-4df9-a63a-540b51c1c0ce"
    />
  );
};

export default EnchargeWidget;
