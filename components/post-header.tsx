'use client';

import { format, formatDistanceToNow, isValid } from 'date-fns';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import type React from 'react';
import { useEffect, useState } from 'react';

const URL_PROTOCOL_REGEX = /^https?:\/\//;

interface PostHeaderProps {
  title: string;
  publishedAt: Date;
  updatedAt: Date;
  location: string;
}

const PostHeader: React.FC<PostHeaderProps> = ({
  title,
  publishedAt: _publishedAt,
  updatedAt: _updatedAt,
  location: _location,
}) => (
  <div className="mb-8">
    {/* Author Badge */}
    <Link
      className="mb-4 inline-block rounded-full bg-slate-100 px-1.5 py-0.5 text-slate-500 text-xs transition-colors hover:bg-slate-200 dark:bg-slate-800 dark:text-slate-400 dark:hover:bg-slate-700"
      href="/about"
    >
      <PERSON>
    </Link>

    {/* Title */}
    <h1 className="mb-6 font-sans font-semibold text-2xl text-slate-800 dark:text-slate-100">
      {title}
    </h1>
  </div>
);

export const PostMetadata: React.FC<Omit<PostHeaderProps, 'title'>> = ({
  publishedAt,
  updatedAt,
  location,
}) => {
  const [copied, setCopied] = useState(false);
  const [url, setUrl] = useState('');
  const pathname = usePathname();

  useEffect(() => {
    const cleanUrl =
      typeof window !== 'undefined'
        ? `${window.location.origin}${pathname}`
        : '';
    setUrl(cleanUrl);
  }, [pathname]);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(url).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };

  // Safely format date if valid
  const formatDate = (date: Date) => {
    if (!(date && isValid(date))) {
      return 'Unknown date';
    }
    return format(date, 'MMM d, yyyy');
  };

  // Safely format relative date if valid
  const formatRelativeDate = (date: Date) => {
    if (!(date && isValid(date))) {
      return '';
    }
    return `(${formatDistanceToNow(date, { addSuffix: true })})`;
  };

  const isUpdated =
    updatedAt &&
    isValid(updatedAt) &&
    isValid(publishedAt) &&
    updatedAt > publishedAt;

  return (
    <div className="mt-8 border-slate-200 border-t pt-6 dark:border-slate-800">
      <div className="flex flex-col gap-3">
        <div className="flex flex-wrap gap-4 text-slate-500 text-xs dark:text-slate-400">
          <span>
            Published: {formatDate(publishedAt)}{' '}
            {formatRelativeDate(publishedAt)}
          </span>
          {isUpdated && (
            <span>
              Updated: {formatDate(updatedAt)} {formatRelativeDate(updatedAt)}
            </span>
          )}
          {location && <span>{location}</span>}
        </div>
        <button
          aria-label="Copy link to clipboard"
          className="self-start whitespace-nowrap rounded-full bg-slate-100 px-2 py-0.5 text-[10px] text-slate-500 transition-colors hover:bg-slate-200 dark:bg-slate-800 dark:text-slate-400 dark:hover:bg-slate-700"
          onClick={copyToClipboard}
          type="button"
        >
          <span className="flex items-center gap-1.5">
            <span className="font-medium">
              {copied ? 'Copied!' : 'Copy link'}
            </span>
            <span>•</span>
            <span className="opacity-75">
              {url.replace(URL_PROTOCOL_REGEX, '')}
            </span>
          </span>
        </button>
      </div>
    </div>
  );
};

export default PostHeader;
