import {
  <PERSON>,
  <PERSON><PERSON><PERSON>wise,
  <PERSON>Logo,
  Laptop,
  LinkedinLogo,
  MapPin,
  XLogo,
} from '@phosphor-icons/react/dist/ssr';
import Image from 'next/image';
import LocationDistance from '@/components/location-distance';

const ProfileCard = () => {
  return (
    <div className="w-full max-w-lg rounded-lg bg-gray-900 p-4 text-white sm:w-auto sm:p-6">
      <div className="mb-4 flex flex-col items-center sm:flex-row sm:items-start sm:justify-between">
        <div className="mb-4 text-center sm:mb-0 sm:text-left">
          <h2 className="font-bold text-xl sm:text-2xl">
            👋 Hi! I'm <PERSON>
          </h2>
          <p className="mt-1 text-gray-300 text-xs">
            Founder, growth marketer, designer, developer, and writer interested
            in calm micro software and digital media businesses.
          </p>
        </div>
        <div className="h-20 w-20 flex-shrink-0 overflow-hidden rounded-full bg-gray-700 sm:h-16 sm:w-16">
          <Image
            alt="<PERSON>"
            className="h-full w-full object-cover object-center"
            height={80}
            src="/images/tomaslau.jpg"
            width={80}
          />
        </div>
      </div>

      <div className="mb-4 text-xs sm:text-sm">
        <p className="mb-2 flex items-center text-gray-300">
          <MapPin className="mr-2 flex-shrink-0" size={18} weight="duotone" />
          Alicante, Spain <LocationDistance />
        </p>
        <p className="mb-2 flex items-start text-gray-300">
          <Laptop
            className="mt-1 mr-2 flex-shrink-0"
            size={18}
            weight="duotone"
          />
          <span>
            Partner at{' '}
            <a
              className="text-emerald-500 hover:underline"
              href="https://craftled.com"
              rel="noopener noreferrer"
              target="_blank"
            >
              Craftled
            </a>{' '}
            & Growth Manager at{' '}
            <a
              className="text-emerald-500 hover:underline"
              href="https://www.getrewardful.com/?via=tomaslau"
              rel="noopener noreferrer"
              target="_blank"
            >
              Rewardful
            </a>
          </span>
        </p>
        <p className="mb-2 flex items-start text-gray-300">
          <Basketball
            className="mt-1 mr-2 flex-shrink-0"
            size={18}
            weight="duotone"
          />
          <span>
            Interests: Books, indie hacking, traveling, weightlifting,
            basketball
          </span>
        </p>
        <p className="flex items-center text-gray-300">
          <ClockClockwise
            className="mr-2 flex-shrink-0"
            size={18}
            weight="duotone"
          />
          <a
            className="text-emerald-500 hover:underline"
            href="https://tomaslau.com/now"
            rel="noopener noreferrer"
            target="_blank"
          >
            What I'm doing now
          </a>
        </p>
      </div>

      <div className="flex flex-wrap justify-center gap-2 sm:justify-start">
        <a
          className="flex items-center rounded-full bg-gray-800 px-3 py-1 text-sm transition-colors hover:bg-gray-700"
          href="https://www.google.com/search?kgmid=/g/11hbm70slz"
          rel="noopener"
          target="_blank"
        >
          <span className="mr-1">
            <GoogleLogo size={18} weight="duotone" />
          </span>{' '}
          Google
        </a>
        <a
          className="flex items-center rounded-full bg-gray-800 px-3 py-1 text-sm transition-colors hover:bg-gray-700"
          href="https://www.perplexity.ai/?s=o&q=Tomas%20Laurinavicius"
          rel="noopener"
          target="_blank"
        >
          <span className="mr-1">
            <svg
              fill="none"
              height="18"
              viewBox="0 0 16 18"
              width="16"
              xmlns="http://www.w3.org/2000/svg"
            >
              <title>GitLab</title>
              <path
                clipRule="evenodd"
                d="M2.10805 0L7.23361 4.72244V4.72135V0.0108968H8.23135V4.74357L13.3799 0V5.38428H15.4937V13.1506H13.3864V17.945L8.23135 13.416V17.997H7.23361V13.4907L2.11386 18V13.1506H0V5.38428H2.10805V0ZM6.48142 6.36984H0.997741V12.165H2.11261V10.337L6.48142 6.36984ZM3.11155 10.7743V15.8008L7.23361 12.1702V7.03031L3.11155 10.7743ZM8.26006 12.1222V7.02547L12.3833 10.7697V13.1506H12.3886V15.7495L8.26006 12.1222ZM13.3864 12.165H14.4959V6.36984H9.05309L13.3864 10.2959V12.165ZM12.3821 5.38428V2.26699L8.99869 5.38428H12.3821ZM6.48917 5.38428H3.1058V2.26699L6.48917 5.38428Z"
                fill="white"
                fillRule="evenodd"
              />
            </svg>
          </span>{' '}
          Perplexity
        </a>
        <a
          className="flex items-center rounded-full bg-gray-800 px-3 py-1 text-sm transition-colors hover:bg-gray-700"
          href="https://www.linkedin.com/in/tomaslau/"
          rel="noopener"
          target="_blank"
        >
          <span className="mr-1">
            <LinkedinLogo size={18} weight="duotone" />
          </span>{' '}
          LinkedIn
        </a>
        <a
          className="flex items-center rounded-full bg-gray-800 px-3 py-1 text-sm transition-colors hover:bg-gray-700"
          href="https://x.com/tomaslaucom"
          rel="noopener"
          target="_blank"
        >
          <span>
            <XLogo size={18} weight="duotone" />
          </span>
        </a>
      </div>
    </div>
  );
};

export default ProfileCard;
