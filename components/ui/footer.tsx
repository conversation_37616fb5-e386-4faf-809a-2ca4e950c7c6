export default function Footer() {
  return (
    <footer className="border-slate-200 border-t dark:border-slate-800">
      <div className="py-8">
        <div className="text-center md:flex md:items-center md:justify-between">
          {/* Social links */}
          <ul className="mb-4 inline-flex space-x-2 md:order-1 md:mb-0 md:ml-4">
            <li>
              <a
                aria-label="X"
                className="flex items-center justify-center text-emerald-500 transition duration-150 ease-in-out hover:text-emerald-600"
                href="https://x.com/tomaslaucom"
                rel="noopener noreferrer"
                target="_blank"
              >
                <svg
                  className="h-6 w-6 fill-current"
                  viewBox="0 0 256 256"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>Twitter/X</title>
                  <path d="M208,216H160L48,40H96Z" opacity="0.2" />
                  <path d="M214.75,211.71l-62.6-98.38,61.77-67.95a8,8,0,0,0-11.84-10.76L143.24,99.34,102.75,35.71A8,8,0,0,0,96,32H48a8,8,0,0,0-6.75,12.3l62.6,98.37-61.77,68a8,8,0,1,0,11.84,10.76l58.84-64.72,40.49,63.63A8,8,0,0,0,160,224h48a8,8,0,0,0,6.75-12.29ZM164.39,208,62.57,48h29L193.43,208Z" />
                </svg>
              </a>
            </li>

            <li>
              <a
                aria-label="GitHub"
                className="flex items-center justify-center text-emerald-500 transition duration-150 ease-in-out hover:text-emerald-600"
                href="https://github.com/tomaslau"
                rel="noopener noreferrer"
                target="_blank"
              >
                <svg
                  className="h-6 w-6 fill-current"
                  viewBox="0 0 256 256"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>GitHub</title>
                  <path
                    d="M208,104v8a48,48,0,0,1-48,48H136a32,32,0,0,1,32,32v40H104V192a32,32,0,0,1,32-32H112a48,48,0,0,1-48-48v-8a49.28,49.28,0,0,1,8.51-27.3A51.92,51.92,0,0,1,76,32a52,52,0,0,1,43.83,24h32.34A52,52,0,0,1,196,32a51.92,51.92,0,0,1,3.49,44.7A49.28,49.28,0,0,1,208,104Z"
                    opacity="0.2"
                  />
                  <path d="M216,104v8a56,56,0,0,1-56,56H136a24,24,0,0,0-24,24v40a8,8,0,0,1-16,0V192a40,40,0,0,1,40-40h24a40,40,0,0,0,40-40v-8a40,40,0,0,0-6.65-22.2,4,4,0,0,1-.24-4A52,52,0,0,0,196,32a4,4,0,0,1-3.37,1.67l-.32,0a59.85,59.85,0,0,0-29.81,9.07,4,4,0,0,1-3.84-.09A100.07,100.07,0,0,0,128,40a100.07,100.07,0,0,0-30.66,2.65,4,4,0,0,1-3.84.09A59.85,59.85,0,0,0,63.69,33.67L63.37,33.67A4,4,0,0,1,60,32a52,52,0,0,0,2.11,45.78,4,4,0,0,1-.24,4A40,40,0,0,0,56,104v8a40,40,0,0,0,40,40h24a40,40,0,0,1,40,40v40a8,8,0,0,1-16,0V192a24,24,0,0,0-24-24H96a56,56,0,0,1-56-56v-8a55.75,55.75,0,0,1,8.31-29.32A68,68,0,0,1,52,32c.42,0,.85,0,1.28,0a75.83,75.83,0,0,1,36.26,10.33A116.11,116.11,0,0,1,128,40a116.11,116.11,0,0,1,38.46,2.33A75.83,75.83,0,0,1,202.72,32c.43,0,.86,0,1.28,0a68,68,0,0,1,3.69,42.68A55.75,55.75,0,0,1,216,104Z" />
                </svg>
              </a>
            </li>
            <li>
              <a
                aria-label="LinkedIn"
                className="flex items-center justify-center text-emerald-500 transition duration-150 ease-in-out hover:text-emerald-600"
                href="https://www.linkedin.com/in/tomaslau/"
                rel="noopener noreferrer"
                target="_blank"
              >
                <svg
                  className="h-6 w-6 fill-current"
                  viewBox="0 0 256 256"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>LinkedIn</title>
                  <path
                    d="M224,40V216a8,8,0,0,1-8,8H40a8,8,0,0,1-8-8V40a8,8,0,0,1,8-8H216A8,8,0,0,1,224,40Z"
                    opacity="0.2"
                  />
                  <path d="M216,24H40A16,16,0,0,0,24,40V216a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V40A16,16,0,0,0,216,24Zm0,192H40V40H216V216ZM96,112v64a8,8,0,0,1-16,0V112a8,8,0,0,1,16,0Zm88,28v36a8,8,0,0,1-16,0V140a20,20,0,0,0-40,0v36a8,8,0,0,1-16,0V112a8,8,0,0,1,15.79-1.78A36,36,0,0,1,184,140ZM100,84A12,12,0,1,1,88,72,12,12,0,0,1,100,84Z" />
                </svg>
              </a>
            </li>
          </ul>

          {/* Copyright */}
          <div className="text-slate-500 text-xs dark:text-slate-400">
            1991-{new Date().getFullYear()} © Growthlog by Tomas Laurinavicius.
            Standing on the shoulders of giants.
          </div>
        </div>
      </div>
    </footer>
  );
}
