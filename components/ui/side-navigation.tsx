'use client';

import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import HeroImage from '@/public/images/tomaslau.jpg';
import ThemeToggle from './theme-toggle';

export default function SideNavigation() {
  const pathname = usePathname();

  return (
    <div className="no-scrollbar sticky top-0 h-screen w-16 shrink-0 overflow-y-auto border-slate-200 border-r md:w-24 dark:border-slate-800">
      <div className="flex h-full flex-col justify-between after:mt-auto after:flex-1">
        <div className="flex-1">
          {pathname !== '/' && (
            <div className="my-4 flex justify-center">
              <Link href="/">
                <Image
                  alt="Growthlog by <PERSON>"
                  className="rounded-full"
                  height={36}
                  priority
                  src={HeroImage}
                  width={36}
                />
              </Link>
            </div>
          )}
        </div>

        <div className="flex flex-1 grow items-center">
          <nav className="w-full">
            <ul className="space-y-4">
              <li className="relative flex h-6 w-full items-center justify-center py-2 after:absolute after:top-0 after:right-0 after:bottom-0 after:w-0.5">
                {/* Light switch */}
                <ThemeToggle />
              </li>
              <li className="py-2">
                <Link
                  className={`relative flex h-6 w-full items-center justify-center after:absolute after:top-0 after:right-0 after:bottom-0 after:w-0.5 ${
                    pathname !== '/about' &&
                    pathname !== '/subscribe' &&
                    pathname !== '/newsletter' &&
                    pathname !== '/now' &&
                    pathname !== '/blog' &&
                    pathname !== '/blog/favorites'
                      ? 'text-emerald-500 after:bg-emerald-500'
                      : 'text-slate-400 hover:text-slate-500 dark:text-slate-500 dark:hover:text-slate-400'
                  }`}
                  href="/"
                >
                  <span className="sr-only">Home</span>
                  <svg
                    className="fill-current"
                    height="19"
                    width="21"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>Home</title>
                    <path d="M4 7v11h13V7l-6.5-5z" fillOpacity=".16" />
                    <path d="m10.433 3.242-8.837 6.56L.404 8.198l10.02-7.44L20.59 8.194l-1.18 1.614-8.977-6.565ZM16 17V9h2v10H3V9h2v8h11Z" />
                  </svg>
                </Link>
              </li>
              <li className="py-2">
                <Link
                  className={`relative flex h-6 w-full items-center justify-center after:absolute after:top-0 after:right-0 after:bottom-0 after:w-0.5 ${
                    pathname === '/about'
                      ? 'text-emerald-500 after:bg-emerald-500'
                      : 'text-slate-400 hover:text-slate-500 dark:text-slate-500 dark:hover:text-slate-400'
                  }`}
                  href="/about"
                >
                  <span className="sr-only">About</span>
                  <svg
                    className="fill-current"
                    height="20"
                    width="20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>About</title>
                    <path
                      d="M10 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8Z"
                      fillOpacity=".16"
                    />
                    <path d="M9.41 11.58c.26 0 .45-.08.57-.23.12-.16.18-.35.18-.58 0-.23-.06-.42-.18-.58-.12-.16-.31-.23-.57-.23-.26 0-.45.08-.57.23-.12.16-.18.35-.18.58 0 .23.06.42.18.58.12.15.31.23.57.23ZM10 2C8.896 2 8 2.896 8 4s.896 2 2 2 2-.896 2-2-.896-2-2-2Zm1 4H9v8h2V6Z" />
                  </svg>
                </Link>
              </li>

              <li className="py-2">
                <Link
                  className={`relative flex h-6 w-full items-center justify-center after:absolute after:top-0 after:right-0 after:bottom-0 after:w-0.5 ${
                    pathname === '/newsletter'
                      ? 'text-emerald-500 after:bg-emerald-500'
                      : 'text-slate-400 hover:text-slate-500 dark:text-slate-500 dark:hover:text-slate-400'
                  }`}
                  href="/newsletter"
                >
                  <span className="sr-only">Newsletter</span>
                  <svg
                    className="fill-current"
                    height="17"
                    width="21"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>Newsletter</title>
                    <path
                      d="M0.992188 1.55953C0.992188 1.29689 1.2051 1.08398 1.46773 1.08398H19.5384C19.801 1.08398 20.0139 1.29689 20.0139 1.55953V14.8747C20.0139 15.1374 19.801 15.3503 19.5384 15.3503H1.46773C1.2051 15.3503 0.992188 15.1374 0.992188 14.8747V1.55953Z"
                      strokeOpacity="0.16"
                    />
                    <path d="M19.0137 2.08398V1.55953C19.0137 1.84835 18.7826 2.08398 18.4984 2.08398H2.50619C2.22201 2.08398 1.99088 1.84835 1.99088 1.55953V2.08398L10.5023 7.86729L19.0137 2.08398Z" />
                  </svg>
                </Link>
              </li>
              <li className="py-2">
                <Link
                  className={`relative flex h-6 w-full items-center justify-center after:absolute after:top-0 after:right-0 after:bottom-0 after:w-0.5 ${
                    pathname === '/now'
                      ? 'text-emerald-500 after:bg-emerald-500'
                      : 'text-slate-400 hover:text-slate-500 dark:text-slate-500 dark:hover:text-slate-400'
                  }`}
                  href="/now"
                >
                  <span className="sr-only">Now</span>
                  <svg
                    className="fill-current"
                    height="21"
                    width="15"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>Now</title>
                    <path
                      d="M1.125 1.4648C1.125 1.20217 1.33791 0.989258 1.60054 0.989258H13.0136C13.2762 0.989258 13.4891 1.20217 13.4891 1.4648V6.10485C13.4891 6.48322 13.3388 6.84608 13.0713 7.11363L9.68478 10.5001L13.0713 13.8866C13.3388 14.1542 13.4891 14.517 13.4891 14.8954V19.5355C13.4891 19.7981 13.2762 20.011 13.0136 20.011H1.60054C1.33791 20.011 1.125 19.7981 1.125 19.5355V14.8954C1.125 14.517 1.27531 14.1542 1.54285 13.8866L4.92935 10.5001L1.54285 7.11363C1.2753 6.84608 1.125 6.48322 1.125 6.10485V1.4648Z"
                      fillOpacity="0.16"
                    />
                    <path d="M2.125 2.4648V5.60485L5.19238 8.67223C5.58291 9.06275 5.58291 9.93738 5.19238 10.3279L2.125 13.3953V18.5355H12.4891V13.3953L9.42174 10.3279C9.03122 9.93738 9.03122 9.06275 9.42174 8.67223L12.4891 5.60485V2.4648H2.125Z" />
                  </svg>
                </Link>
              </li>
              {/*<li className="py-2">
                <Link
                  href="/blog"
                  className={`w-full h-6 flex items-center justify-center relative after:absolute after:w-0.5 after:right-0 after:top-0 after:bottom-0 ${
                    pathname === "/blog"
                      ? "text-emerald-500 after:bg-emerald-500"
                      : "text-slate-400 hover:text-slate-500 dark:text-slate-500 dark:hover:text-slate-400"
                  }`}
                >
                  <span className="sr-only">Blog</span>
                  <svg
                    width="17"
                    height="21"
                    className="fill-current"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>Blog</title>
                    <path
                      d="M1.08594 1.4648C1.08594 1.20217 1.29885 0.989258 1.56148 0.989258H14.8767C15.1393 0.989258 15.3522 1.20217 15.3522 1.4648V19.5355C15.3522 19.7981 15.1393 20.011 14.8767 20.011H13.4501L8.21909 15.2556L2.98811 20.011H1.56148C1.29885 20.011 1.08594 19.7981 1.08594 19.5355V1.4648Z"
                      fillOpacity=".16"
                    />
                    <path d="M0.132812 1.46472C0.132812 0.676809 0.771538 0.0380859 1.55944 0.0380859H14.8747C15.6626 0.0380859 16.3013 0.676809 16.3013 1.46472V19.5354C16.3013 20.3233 15.6626 20.962 14.8747 20.962H13.0803L8.21705 16.5408L3.35377 20.962H1.55944C0.771537 20.962 0.132812 20.3233 0.132812 19.5354V1.46472ZM2.03499 1.94026V19.0598H2.61838L8.21705 13.9701L13.8157 19.0598H14.3991V1.94026H2.03499Z" />
                    <path d="M3.9375 3.84277H12.4973V5.74495H3.9375V3.84277Z" />
                  </svg>
                </Link>
                </li>*/}
              {/* <li className="py-2">
                <Link
                  href="/blog/favorites"
                  className={`w-full h-6 flex items-center justify-center relative after:absolute after:w-0.5 after:right-0 after:top-0 after:bottom-0 ${
                    pathname === "/blog/favorites"
                      ? "text-emerald-500 after:bg-emerald-500"
                      : "text-slate-400 hover:text-slate-500 dark:text-slate-500 dark:hover:text-slate-400"
                  }`}
                >
                  <span className="sr-only">Favorites</span>

                  <svg
                    width="21"
                    height="19"
                    className="fill-current"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>Favorites</title>
                    <path
                      d="M11.9236 2.47235C13.7807 0.615235 16.7579 0.581439 18.615 2.43855C20.4721 4.29566 20.4721 7.30664 18.615 9.16375L10.4969 17.2142L2.37891 9.16375C0.521797 7.30664 0.521797 4.29566 2.37891 2.43855C4.23602 0.581439 7.2132 0.615235 9.07031 2.47235L10.4969 3.89898L11.9236 2.47235Z"
                      fillOpacity=".16"
                    />
                    <path d="M19.2895 1.76694C17.0522 -0.470431 13.4727 -0.418905 11.2531 1.80073L10.499 2.55484L9.74487 1.80073C7.52523 -0.418905 3.94579 -0.470431 1.70842 1.76694C-0.520114 3.99547 -0.520114 7.60864 1.70842 9.83718L1.71124 9.83998L10.499 18.5546L19.2895 9.83718C21.5181 7.60865 21.5181 3.99547 19.2895 1.76694ZM12.5981 3.14577C14.0927 1.65119 16.4676 1.63512 17.9445 3.11198C19.4297 4.59721 19.4302 7.00496 17.9459 8.49076C17.9454 8.49122 17.945 8.49168 17.9445 8.49214L10.499 15.8756L3.05346 8.49214C3.05301 8.49168 3.05255 8.49122 3.05209 8.49076C1.56777 7.00496 1.56823 4.59721 3.05346 3.11198C4.53032 1.63512 6.90524 1.65119 8.39983 3.14577L10.499 5.24492L12.5981 3.14577Z" />
                    <path d="M5.27038 6.75267C6.05829 6.75267 6.69701 6.11395 6.69701 5.32604C6.69701 4.53814 6.05829 3.89941 5.27038 3.89941C4.48247 3.89941 3.84375 4.53814 3.84375 5.32604C3.84375 6.11395 4.48247 6.75267 5.27038 6.75267Z" />
                  </svg>
                </Link>
                </li> */}
            </ul>
          </nav>
        </div>
      </div>
    </div>
  );
}
