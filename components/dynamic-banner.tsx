'use client';

import {
  Cactus,
  Calendar,
  Clock,
  MapPin,
  SealCheck,
} from '@phosphor-icons/react';
import type React from 'react';
import { formatDate } from '@/utils/date-formatter';

interface DynamicBannerProps {
  title: string;
  description: string;
  websiteUrl?: string;
  publishedAt: string;
  updatedAt?: string;
  location: string;
  readTime: string;
}

const DynamicBanner: React.FC<DynamicBannerProps> = ({
  title,
  description,
  websiteUrl = 'tomaslau.com',
  publishedAt,
  updatedAt,
  location,
  readTime,
}) => (
  <div className="relative mb-4 w-full overflow-hidden rounded-lg border border-slate-200 bg-slate-100 sm:mb-8 sm:aspect-[1400/735] sm:h-auto dark:border-slate-700 dark:bg-slate-800">
    <div
      className="absolute inset-0"
      style={{
        backgroundImage: `
        linear-gradient(to right, rgba(0,0,0,0.05) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(0,0,0,0.05) 1px, transparent 1px)
      `,
        backgroundSize: '10px 10px',
        opacity: 0.4,
      }}
    />
    <div className="relative z-10 flex min-h-full flex-col p-2 sm:h-full sm:p-3 md:p-8">
      <div className="flex flex-grow flex-col justify-between rounded-lg border border-slate-200 bg-white p-2 sm:p-3 md:p-8 dark:border-slate-700 dark:bg-slate-900">
        <div className="space-y-1 sm:space-y-2 md:space-y-4">
          <div className="mb-1 flex items-center sm:mb-2">
            <Cactus
              className="mr-2 text-emerald-500"
              size={24}
              weight="duotone"
            />
            <span className="mr-2 font-geist font-semibold text-slate-800 text-sm sm:mr-4 sm:text-base md:text-xl dark:text-white">
              Growthlog
            </span>
            <div className="flex items-center rounded-full bg-slate-100 px-2 py-1 dark:bg-slate-800">
              <SealCheck
                className="mr-1 text-blue-500 dark:text-blue-400"
                size={12}
                weight="fill"
              />
              <span className="font-geist text-slate-500 text-xs dark:text-slate-400">
                {websiteUrl}
              </span>
            </div>
          </div>
          <h1 className="font-bold font-geist text-lg text-slate-800 leading-tight sm:text-xl md:text-3xl dark:text-white">
            {title}
          </h1>
          <p className="max-w-3xl font-geist text-slate-500 text-sm sm:text-base dark:text-slate-400">
            {description}
          </p>
        </div>
        <div className="-mx-1 sm:-mx-2 mt-2 flex flex-wrap items-center text-slate-500 text-xs sm:mt-4 dark:text-slate-400">
          <span className="mb-1 flex items-center px-1 sm:px-2">
            <Calendar className="mr-1 flex-shrink-0" size={14} />
            {formatDate(publishedAt)}
          </span>
          {updatedAt && (
            <span className="mb-1 flex items-center px-1 sm:px-2">
              <Clock className="mr-1 flex-shrink-0" size={14} />
              Updated: {formatDate(updatedAt)}
            </span>
          )}
          <span className="mb-1 flex items-center px-1 sm:px-2">
            <MapPin className="mr-1 flex-shrink-0" size={14} />
            {location}
          </span>
          <span className="mb-1 flex items-center px-1 sm:px-2">
            <Clock className="mr-1 flex-shrink-0" size={14} />
            {readTime}
          </span>
        </div>
      </div>
    </div>
  </div>
);

export default DynamicBanner;
