import { projects } from '@/data';

export default function Projects() {
  return (
    <section>
      <h2 className="mb-6 font-[650] font-geist text-xl">Projects</h2>
      <div className="grid gap-3 lg:grid-cols-3 min-[580px]:grid-cols-2">
        {projects.map((project) => (
          <article className="group relative" key={project.title}>
            <a
              className="block rounded-lg border border-gray-200 p-3 transition-colors duration-150 hover:bg-gray-50 dark:border-gray-800 dark:hover:bg-gray-800/50"
              href={project.url}
              rel={`noopener${project.nofollow ? ' nofollow' : ''}`}
              target="_blank"
            >
              <div className="mb-1 flex items-center justify-between">
                <h3 className="font-medium text-[13px] text-gray-900 transition-colors duration-150 group-hover:text-blue-600 dark:text-gray-100 dark:group-hover:text-blue-400">
                  {project.title}
                  <span className="ml-2 rounded-full bg-gray-100 px-1.5 py-0.5 text-[10px] text-gray-400 dark:bg-gray-800 dark:text-gray-500">
                    {project.year}
                  </span>
                  <span
                    className={`ml-2 rounded-full px-1.5 py-0.5 text-[10px] ${
                      project.status === 'Active'
                        ? 'bg-emerald-100 text-emerald-600 dark:bg-emerald-800/30 dark:text-emerald-400'
                        : 'bg-gray-100 text-gray-500 dark:bg-gray-800 dark:text-gray-400'
                    }`}
                  >
                    {project.status}
                  </span>
                </h3>
                <svg
                  className="h-3 w-3 text-gray-400 transition-transform duration-150 group-hover:translate-x-0.5 group-hover:text-gray-600 dark:text-gray-600 dark:group-hover:text-gray-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>External link</title>
                  <path
                    clipRule="evenodd"
                    d="M5.22 14.78a.75.75 0 001.06 0l7.22-7.22v5.69a.75.75 0 001.5 0v-7.5a.75.75 0 00-.75-.75h-7.5a.75.75 0 000 1.5h5.69l-7.22 7.22a.75.75 0 000 1.06z"
                    fillRule="evenodd"
                  />
                </svg>
              </div>
              <div className="text-gray-500 text-sm dark:text-gray-400">
                {project.description}
              </div>
            </a>
          </article>
        ))}
      </div>
    </section>
  );
}
