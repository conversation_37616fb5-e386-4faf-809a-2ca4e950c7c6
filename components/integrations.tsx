import Image from 'next/image';
import Icon01 from '@/public/images/integration-icon-01.svg';
import Icon02 from '@/public/images/integration-icon-02.svg';
import Icon03 from '@/public/images/integration-icon-03.svg';
import Icon04 from '@/public/images/integration-icon-04.svg';
import Icon05 from '@/public/images/integration-icon-05.svg';
import Icon06 from '@/public/images/integration-icon-06.svg';

export default function Integrations() {
  return (
    <section className="relative">
      {/* Bottom vertical line */}
      <div
        aria-hidden="true"
        className="-translate-x-1/2 absolute bottom-0 left-1/2 hidden h-8 w-0.5 bg-slate-800 md:block"
      />
      <div className="mx-auto max-w-6xl px-4 sm:px-6">
        <div className="border-slate-800 border-t py-12 md:py-20">
          {/* Section header */}
          <div className="mx-auto max-w-3xl pb-12 text-center">
            <h2 className="h2 font-hkgrotesk">An ecosystem of integrations</h2>
          </div>
          {/* Logo animation */}
          <div className="relative flex flex-col items-center p-16">
            {/* Blurred dots */}
            <svg
              className="-translate-y-1/2 absolute top-1/2"
              height="93"
              width="557"
              xmlns="http://www.w3.org/2000/svg"
            >
              <title>Decorative background dots</title>
              <defs>
                <filter
                  filterUnits="objectBoundingBox"
                  height="200%"
                  id="hlogo-blurreddots-a"
                  width="200%"
                  x="-50%"
                  y="-50%"
                >
                  <feGaussianBlur in="SourceGraphic" stdDeviation="2" />
                </filter>
                <filter
                  filterUnits="objectBoundingBox"
                  height="200%"
                  id="blurreddots-b"
                  width="200%"
                  x="-50%"
                  y="-50%"
                >
                  <feGaussianBlur in="SourceGraphic" stdDeviation="2" />
                </filter>
                <filter
                  filterUnits="objectBoundingBox"
                  height="400%"
                  id="blurreddots-c"
                  width="400%"
                  x="-150%"
                  y="-150%"
                >
                  <feGaussianBlur in="SourceGraphic" stdDeviation="6" />
                </filter>
                <filter
                  filterUnits="objectBoundingBox"
                  height="400%"
                  id="blurreddots-d"
                  width="400%"
                  x="-150%"
                  y="-150%"
                >
                  <feGaussianBlur in="SourceGraphic" stdDeviation="4" />
                </filter>
                <filter
                  filterUnits="objectBoundingBox"
                  height="400%"
                  id="blurreddots-e"
                  width="400%"
                  x="-150%"
                  y="-150%"
                >
                  <feGaussianBlur in="SourceGraphic" stdDeviation="4" />
                </filter>
                <filter
                  filterUnits="objectBoundingBox"
                  height="200%"
                  id="blurreddots-f"
                  width="200%"
                  x="-50%"
                  y="-50%"
                >
                  <feGaussianBlur in="SourceGraphic" stdDeviation="2" />
                </filter>
                <filter
                  filterUnits="objectBoundingBox"
                  height="300%"
                  id="blurreddots-g"
                  width="300%"
                  x="-100%"
                  y="-100%"
                >
                  <feGaussianBlur in="SourceGraphic" stdDeviation="4" />
                </filter>
                <filter
                  filterUnits="objectBoundingBox"
                  height="400%"
                  id="blurreddots-h"
                  width="400%"
                  x="-150%"
                  y="-150%"
                >
                  <feGaussianBlur in="SourceGraphic" stdDeviation="6" />
                </filter>
                <filter
                  filterUnits="objectBoundingBox"
                  height="400%"
                  id="blurreddots-i"
                  width="400%"
                  x="-150%"
                  y="-150%"
                >
                  <feGaussianBlur in="SourceGraphic" stdDeviation="4" />
                </filter>
                <filter
                  filterUnits="objectBoundingBox"
                  height="250%"
                  id="blurreddots-j"
                  width="250%"
                  x="-75%"
                  y="-75%"
                >
                  <feGaussianBlur in="SourceGraphic" stdDeviation="2" />
                </filter>
              </defs>
              <g fill="none" fillRule="evenodd">
                <g className="fill-indigo-600" transform="translate(437 8)">
                  <circle
                    cx="6"
                    cy="66"
                    fillOpacity=".64"
                    filter="url(#blurreddots-a)"
                    r="6"
                  />
                  <circle
                    cx="90"
                    cy="6"
                    fillOpacity=".32"
                    filter="url(#blurreddots-b)"
                    r="6"
                  />
                  <circle
                    cx="90"
                    cy="66"
                    fillOpacity=".64"
                    filter="url(#blurreddots-c)"
                    r="6"
                  />
                  <circle
                    cx="6"
                    cy="36"
                    fillOpacity=".32"
                    filter="url(#blurreddots-d)"
                    r="4"
                  />
                  <circle
                    cx="60"
                    cy="36"
                    fillOpacity=".32"
                    filter="url(#blurreddots-e)"
                    r="4"
                  />
                  <circle cx="34" cy="22" fillOpacity=".64" r="2" />
                  <circle cx="34" cy="50" fillOpacity=".32" r="2" />
                  <circle cx="118" cy="22" fillOpacity=".64" r="2" />
                  <circle cx="118" cy="50" fillOpacity=".32" r="2" />
                </g>
                <g
                  className="fill-indigo-600"
                  transform="matrix(-1 0 0 1 120 8)"
                >
                  <circle
                    cx="6"
                    cy="66"
                    fillOpacity=".64"
                    filter="url(#blurreddots-f)"
                    r="6"
                  />
                  <circle
                    cx="90"
                    cy="6"
                    fillOpacity=".32"
                    filter="url(#blurreddots-g)"
                    r="6"
                  />
                  <circle
                    cx="90"
                    cy="66"
                    fillOpacity=".64"
                    filter="url(#blurreddots-h)"
                    r="6"
                  />
                  <circle
                    cx="6"
                    cy="36"
                    fillOpacity=".32"
                    filter="url(#blurreddots-i)"
                    r="4"
                  />
                  <circle
                    cx="60"
                    cy="36"
                    fillOpacity=".64"
                    filter="url(#blurreddots-j)"
                    r="4"
                  />
                  <circle cx="34" cy="22" fillOpacity=".32" r="2" />
                  <circle cx="34" cy="50" fillOpacity=".32" r="2" />
                  <circle cx="118" cy="22" fillOpacity=".64" r="2" />
                  <circle cx="118" cy="50" fillOpacity=".32" r="2" />
                </g>
              </g>
            </svg>
            <div className="relative flex h-32 w-32 items-center justify-center">
              {/* Halo effect */}
              <svg
                className="-translate-x-1/2 -translate-y-1/2 pointer-events-none absolute inset-0 top-1/2 left-1/2 h-auto max-w-[200%] transform"
                height="800"
                viewBox="0 0 800 800"
                width="800"
                xmlns="http://www.w3.org/2000/svg"
              >
                <title>Integration halo effect</title>
                <defs>
                  <linearGradient id="lg-1" x1="50%" x2="50%" y1="0%" y2="100%">
                    <stop offset="0%" stopColor="#0F172A" stopOpacity="0%" />
                    <stop offset="100%" stopColor="#0F172A" />
                  </linearGradient>
                </defs>
                <g className="fill-indigo-600 opacity-75" fillRule="evenodd">
                  <circle className="pulse" cx="400" cy="400" r="200" />
                  <circle className="pulse pulse-1" cx="400" cy="400" r="200" />
                  <circle className="pulse pulse-2" cx="400" cy="400" r="200" />
                  <circle className="pulse pulse-3" cx="400" cy="400" r="200" />
                  <rect fill="url(#lg-1)" height="800" width="800" />
                </g>
              </svg>
              {/* Logo */}
              <svg
                className="h-16 w-16"
                viewBox="0 0 32 32"
                xmlns="http://www.w3.org/2000/svg"
              >
                <title>Integration logo</title>
                <defs>
                  <linearGradient
                    id="a"
                    x1="0%"
                    x2="104.18%"
                    y1="32.443%"
                    y2="50%"
                  >
                    <stop offset="0%" stopColor="#FFF" stopOpacity=".299" />
                    <stop offset="100%" stopColor="#7587E4" stopOpacity="0" />
                  </linearGradient>
                  <linearGradient
                    id="b"
                    x1="18.591%"
                    x2="100%"
                    y1="0%"
                    y2="100%"
                  >
                    <stop offset="0%" stopColor="#818CF8" />
                    <stop offset="100%" stopColor="#C7D2FE" />
                  </linearGradient>
                </defs>
                <g fill="none" fillRule="evenodd">
                  <path d="M16 18.5V32l15.999-9.25V9.25z" fill="#3730A3" />
                  <path d="m0 23 16 9V18.501L0 9.251z" fill="#4F46E5" />
                  <path
                    d="M16 13 0 23l16 9 16-9z"
                    fill="url(#a)"
                    fillOpacity=".64"
                  />
                  <path d="M16 0 0 9.25l16 9.25 15.999-9.25z" fill="url(#b)" />
                </g>
              </svg>
            </div>
          </div>
          {/* Integration boxes */}
          <div className="relative mx-auto mt-10 grid max-w-xs grid-cols-2 gap-6 sm:max-w-md sm:grid-cols-3 md:mt-20 md:max-w-6xl md:grid-cols-6">
            {/* Top vertical line */}
            <div
              aria-hidden="true"
              className="-top-16 -mt-2 -translate-x-1/2 absolute left-1/2 hidden h-8 w-0.5 bg-slate-800 md:block"
            />
            <div
              className="relative flex aspect-square items-center justify-center bg-slate-800 p-2"
              data-aos="fade-up"
            >
              {/* Inner lines */}
              <div
                aria-hidden="true"
                className="-top-10 -translate-x-1/2 absolute inset-0 left-1/2 hidden h-6 w-[calc(100%+24px)] md:block"
              >
                <div className="-translate-x-1/2 absolute left-1/2 h-full w-0.5 bg-slate-800" />
                <div className="absolute right-0 h-0.5 w-1/2 bg-slate-800" />
              </div>
              {/* Circle */}
              <div className="flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-t from-slate-800 to-slate-900">
                {/* Icon */}
                <Image alt="Icon 01" height={46} src={Icon01} width={36} />
              </div>
            </div>
            <div
              className="relative flex aspect-square items-center justify-center bg-slate-800 p-2"
              data-aos="fade-up"
              data-aos-delay="100"
            >
              {/* Inner lines */}
              <div
                aria-hidden="true"
                className="-top-10 -translate-x-1/2 absolute inset-0 left-1/2 hidden h-6 w-[calc(100%+24px)] md:block"
              >
                <div className="-translate-x-1/2 absolute left-1/2 h-full w-0.5 bg-slate-800" />
                <div className="absolute h-0.5 w-full bg-slate-800" />
              </div>
              {/* Circle */}
              <div className="flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-t from-slate-800 to-slate-900">
                {/* Icon */}
                <Image alt="Icon 02" height={46} src={Icon02} width={46} />
              </div>
            </div>
            <div
              className="relative flex aspect-square items-center justify-center bg-slate-800 p-2"
              data-aos="fade-up"
              data-aos-delay="200"
            >
              {/* Inner lines */}
              <div
                aria-hidden="true"
                className="-top-10 -translate-x-1/2 absolute inset-0 left-1/2 hidden h-6 w-[calc(100%+24px)] md:block"
              >
                <div className="-translate-x-1/2 absolute left-1/2 h-full w-0.5 bg-slate-800" />
                <div className="absolute h-0.5 w-full bg-slate-800" />
              </div>
              {/* Circle */}
              <div className="flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-t from-slate-800 to-slate-900">
                {/* Icon */}
                <Image alt="Icon 03" height={45} src={Icon03} width={53} />
              </div>
            </div>
            <div
              className="relative flex aspect-square items-center justify-center bg-slate-800 p-2"
              data-aos="fade-up"
              data-aos-delay="300"
            >
              {/* Inner lines */}
              <div
                aria-hidden="true"
                className="-top-10 -translate-x-1/2 absolute inset-0 left-1/2 hidden h-6 w-[calc(100%+24px)] md:block"
              >
                <div className="-translate-x-1/2 absolute left-1/2 h-full w-0.5 bg-slate-800" />
                <div className="absolute h-0.5 w-full bg-slate-800" />
              </div>
              {/* Circle */}
              <div className="flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-t from-slate-800 to-slate-900">
                {/* Icon */}
                <Image alt="Icon 04" height={46} src={Icon04} width={48} />
              </div>
            </div>
            <div
              className="relative flex aspect-square items-center justify-center bg-slate-800 p-2"
              data-aos="fade-up"
              data-aos-delay="400"
            >
              {/* Inner lines */}
              <div
                aria-hidden="true"
                className="-top-10 -translate-x-1/2 absolute inset-0 left-1/2 hidden h-6 w-[calc(100%+24px)] md:block"
              >
                <div className="-translate-x-1/2 absolute left-1/2 h-full w-0.5 bg-slate-800" />
                <div className="absolute h-0.5 w-full bg-slate-800" />
              </div>
              {/* Circle */}
              <div className="flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-t from-slate-800 to-slate-900">
                {/* Icon */}
                <Image alt="Icon 05" height={48} src={Icon05} width={49} />
              </div>
            </div>
            <div
              className="relative flex aspect-square items-center justify-center bg-slate-800 p-2"
              data-aos="fade-up"
              data-aos-delay="500"
            >
              {/* Inner lines */}
              <div
                aria-hidden="true"
                className="-top-10 -translate-x-1/2 absolute inset-0 left-1/2 hidden h-6 w-[calc(100%+24px)] md:block"
              >
                <div className="-translate-x-1/2 absolute left-1/2 h-full w-0.5 bg-slate-800" />
                <div className="absolute left-0 h-0.5 w-1/2 bg-slate-800" />
              </div>
              {/* Circle */}
              <div className="flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-t from-slate-800 to-slate-900">
                {/* Icon */}
                <Image alt="Icon 06" height={44} src={Icon06} width={48} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
