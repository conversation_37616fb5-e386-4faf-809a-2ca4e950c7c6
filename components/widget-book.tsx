import Image from 'next/image';
import Link from 'next/link';
import Book from '@/public/images/book.png';

export default function WidgetBook() {
  return (
    <div className="even:-rotate-1 rounded-lg border border-slate-200 p-5 odd:rotate-1 dark:border-slate-800 dark:bg-gradient-to-t dark:from-slate-800 dark:to-slate-800/30">
      <div className="mb-3 text-center font-sans font-semibold">Free Book</div>
      <div className="text-center">
        <Link href="/blog/increments">
          <Image
            alt="Increments Book"
            className="inline-flex rotate-3 rounded-lg shadow-lg duration-75 hover:rotate-0"
            height={190}
            src={Book}
            width={148}
          />
        </Link>
      </div>
    </div>
  );
}
