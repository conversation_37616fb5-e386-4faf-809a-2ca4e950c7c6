'use client';

import type React from 'react';
import { useEffect, useState } from 'react';

interface MDXContentProps {
  children: React.ReactNode;
}

export default function MDXContent({ children }: MDXContentProps) {
  const [hasError, setHasError] = useState(false);
  const [errorDetails, setErrorDetails] = useState<Error | null>(null);

  useEffect(() => {
    // Check if children contains any errors or undefined values
    try {
      if (!children) {
        throw new Error('MDX content is empty or undefined');
      }
    } catch (error) {
      setHasError(true);
      setErrorDetails(
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }, [children]);

  if (hasError) {
    return (
      <div className="my-4 rounded-md border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-900/20">
        <p className="mb-2 font-medium text-red-600 dark:text-red-400">
          There was an error rendering the content.
        </p>
        {process.env.NODE_ENV === 'development' && errorDetails && (
          <pre className="overflow-auto rounded bg-red-100 p-2 text-red-500 text-xs dark:bg-red-900/30 dark:text-red-400">
            {errorDetails.message}
          </pre>
        )}
      </div>
    );
  }

  return (
    <div className="prose dark:prose-invert prose-li:-ml-[0.1rem] prose-pre:my-6 prose-h1:mb-6 prose-h2:mb-4 prose-p:mb-4 max-w-none prose-ol:list-outside prose-ul:list-outside prose-ol:list-decimal prose-ul:list-disc prose-ol:space-y-1.5 prose-ul:space-y-1.5 prose-pre:rounded-lg prose-pre:border prose-pre:border-slate-200 prose-code:bg-slate-100 prose-pre:bg-slate-50 prose-pre:p-4 prose-ol:pl-5 prose-ul:pl-5 prose-a:font-medium prose-headings:font-sans prose-headings:font-semibold prose-a:text-emerald-500 prose-code:text-slate-800 prose-h1:text-2xl prose-h2:text-xl prose-headings:text-slate-800 prose-ol:text-slate-500 prose-ol:text-sm prose-p:text-slate-500 prose-p:text-sm prose-strong:text-slate-800 prose-ul:text-slate-500 prose-ul:text-sm prose-a:no-underline hover:prose-a:underline dark:prose-pre:border-slate-600 dark:prose-code:bg-slate-800 dark:prose-pre:bg-slate-900 dark:prose-code:text-slate-200 dark:prose-headings:text-slate-100 dark:prose-ol:text-slate-400 dark:prose-p:text-slate-400 dark:prose-strong:text-white dark:prose-ul:text-slate-400">
      {children}
    </div>
  );
}
